<template>
  <div class="dashboard-content">
    <div class="panels-container">
      <TopLeftPanel />
      <TopMiddlePanel />
      <TopRightPanel />
      <BottomLeftPanel />
      <BottomMiddlePanel />
      <BottomRightPanel2 />
    </div>
  </div>
</template>

<script setup>
import TopLeftPanel from '@/components/strike-effect/TopLeftPanel.vue'
import TopMiddlePanel from '@/components/strike-effect/TopMiddlePanel.vue'
import TopRightPanel from '@/components/strike-effect/TopRightPanel.vue'
import BottomLeftPanel from '@/components/strike-effect/BottomLeftPanel.vue'
import BottomMiddlePanel from '@/components/strike-effect/BottomMiddlePanel.vue'
// import BottomRightPanel from '@/components/strike-effect/BottomRightPanel.vue'
import BottomRightPanel2 from '@/components/strike-effect/BottomRightPanel2.vue'
</script>

<style scoped>
.dashboard-container {
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
  font-family: var(--font-family-base);
}

.main-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  object-fit: cover;
}

.dashboard-content {
  position: relative;
  padding: 20px;
  height: calc(100% - 82px); /* 减去标题高度 */
}

.panels-container {
  display: grid;
  grid-template-columns: 540px 1fr 540px;
  grid-template-rows: repeat(2, 472px);
  gap: 20px;
  justify-content: center;
}
</style>
