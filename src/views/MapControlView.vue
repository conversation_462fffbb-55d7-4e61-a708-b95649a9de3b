<script setup>
import { ref, onMounted, reactive, watch } from 'vue'
import VChart from 'vue-echarts'
import * as echarts from 'echarts/core'
import { use } from 'echarts/core'
import { MapChart } from 'echarts/charts'
import { useRouter } from 'vue-router'
import { useQueryConditionStore } from '@/stores/queryCondition'
import { getRyzsData } from '@/api/dashboard'
import {
  VisualMapComponent,
  TitleComponent,
  TooltipComponent,
  GeoComponent,
  LegendComponent,
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import jcxx from '@/assets/dashboard/backgrounds/jcxx.png'
import yjxx from '@/assets/dashboard/backgrounds/yjxx.png'
import djcx from '@/assets/dashboard/backgrounds/djcx.png'
import ddxx from '@/assets/dashboard/backgrounds/dddb.png'
import guangxi from './mapdata/4500.json'

const queryStore = useQueryConditionStore()
const router = useRouter()

// 注册必要的组件
use([
  MapChart,
  VisualMapComponent,
  TitleComponent,
  TooltipComponent,
  GeoComponent,
  LegendComponent,
  CanvasRenderer,
])

// 获取外部配置
const getExternalConfig = async () => {
  try {
    // 通过动态创建script标签加载外部配置
    return new Promise((resolve) => {
      if (window.externalConfig) {
        return resolve(window.externalConfig)
      }

      const script = document.createElement('script')
      script.src = '/config.js'
      script.onload = () => resolve(window.externalConfig || {})
      script.onerror = () => {
        console.warn('无法加载外部配置文件，使用默认配置')
        resolve({})
      }
      document.head.appendChild(script)
    })
  } catch (error) {
    console.error('加载配置失败:', error)
    return {}
  }
}

// 导航项数据
const navItems = ref([
  { id: 'basic', name: '基础信息', icon: jcxx, url: '' },
  { id: 'warning', name: '预警信息', icon: yjxx, url: '' },
  { id: 'achievement', name: '打击成效', icon: djcx, url: '' },
  { id: 'supervision', name: '督导盯办', icon: ddxx, url: '' },
])

// 当前选中的导航项
const activeNavItem = ref('basic')

// 处理导航项点击
const handleNavClick = (itemId) => {
  activeNavItem.value = itemId

  if (itemId === 'achievement') {
    router.push({ name: 'strike-effect' })
    return
  } else if (itemId === 'supervision') {
    router.push({ name: 'supervision' })
    return
  }

  // 查找当前点击的导航项
  const currentItem = navItems.value.find((item) => item.id === itemId)
  if (currentItem && currentItem.url) {
    // 获取token
    const token = sessionStorage.getItem('access_token')

    // 构建带token的URL
    let url = currentItem.url
    if (token) {
      const separator = url.includes('?') ? '&' : '?'
      url = `${url}${separator}access_token=${token}`
    }

    // 如果有配置URL，则打开新页面
    window.open(url, '_blank')
  }
}

// 初始化导航菜单配置
const initNavConfig = async () => {
  const externalConfig = await getExternalConfig()

  // 如果存在导航配置，则更新导航项的URL
  if (externalConfig.navUrls) {
    navItems.value = navItems.value.map((item) => {
      return {
        ...item,
        url: externalConfig.navUrls[item.id] || '',
      }
    })
  }
}

// 地图层级状态
const mapStack = reactive({
  // 当前层级：1=广西整体，2=地级市，3=县区
  level: 1,
  // 当前选中的地区
  code: '4500',
  name: '广西',
  // 导航历史
  history: [],
  // 地图模式：drill(钻取模式) 或 multiSelect(多选模式)
  mode: 'drill',
  // 多选模式下选中的地市
  selectedCities: [],
})

// 地区代码与名称的映射
const regionMap = {
  4500: '广西',
  4501: '南宁市',
  4502: '柳州市',
  4503: '桂林市',
  4504: '梧州市',
  4505: '北海市',
  4506: '防城港市',
  4507: '钦州市',
  4508: '贵港市',
  4509: '玉林市',
  4510: '百色市',
  4511: '贺州市',
  4512: '河池市',
  4513: '来宾市',
  4514: '崇左市',
}

const chartRef = ref(null)
echarts.registerMap('guangxi', guangxi)
const mapOption = ref({
  title: {
    text: '',
    subtext: '',
    left: 'center',
    top: 10,
    textStyle: {
      color: '#fff',
      fontSize: 24,
      fontWeight: 'bold',
    },
    subtextStyle: {
      color: '#ccc',
      fontSize: 14,
    },
  },
  tooltip: {
    trigger: 'item',
    formatter: (params) => {
      if (mapStack.level === 1 && mapStack.mode === 'drill') {
        return `
          <div style="font-weight:bold;font-size:16px;margin-bottom:5px;">${params.name}</div>
          <div style="display:flex;justify-content:space-between;margin:3px 0;">
            <span>总人数:</span>
            <span style="font-weight:bold;color:#ffad33;">${params.data?.value || 0}人</span>
          </div>
          <div style="display:flex;justify-content:space-between;margin:3px 0;">
            <span>妨害国边境:</span>
            <span style="font-weight:bold;color:#5ac8fa;">${mapDataState.caseCounts.borderViolation}人</span>
          </div>
          <div style="display:flex;justify-content:space-between;margin:3px 0;">
            <span>走私:</span>
            <span style="font-weight:bold;color:#5ac8fa;">${mapDataState.caseCounts.smuggling}人</span>
          </div>
          <div style="display:flex;justify-content:space-between;margin:3px 0;">
            <span>涉毒:</span>
            <span style="font-weight:bold;color:#5ac8fa;">${mapDataState.caseCounts.drugRelated}人</span>
          </div>
          <div style="display:flex;justify-content:space-between;margin:3px 0;">
            <span>涉枪:</span>
            <span style="font-weight:bold;color:#5ac8fa;">${mapDataState.caseCounts.weaponRelated}人</span>
          </div>
        `
      } else if (mapStack.level === 1) {
        // 其他情况（多选模式）
        return `${params.name}: ${params.value || 0}人`
      } else if (mapStack.level === 2) {
        // 第二级：县区
        return params.name
      }
      return params.name
    },
    backgroundColor: 'rgba(0,24,41,0.9)',
    borderColor: '#5ac8fa',
    borderWidth: 1,
    padding: 10,
    textStyle: {
      color: '#fff',
      fontSize: 14,
    },
    extraCssText: 'width: 220px;', // 增加tooltip宽度
  },
  visualMap: {
    show: false,
    type: 'piecewise',
    right: 30,
    bottom: 30,
    pieces: [
      { label: '前5名', color: '#19b76b' },
      { label: '中间', color: '#ffad33' },
      { label: '后3名', color: '#ff5757' },
    ],
    textStyle: {
      color: '#fff',
    },
  },
  geo: [
    {
      layoutCenter: ['50%', '50%'], //位置
      layoutSize: '180%', //大小
      show: true,
      map: 'guangxi',
      roam: false,
      zoom: 0.65,
      aspectScale: 1,
      label: {
        normal: {
          show: true,
          textStyle: {
            color: '#fff',
            fontSize: 14,
            fontWeight: 'bold',
            textShadowColor: 'rgba(0, 0, 0, 0.5)',
            textShadowBlur: 5,
            textShadowOffsetX: 1,
            textShadowOffsetY: 1,
          },
        },
        emphasis: {
          show: true,
          textStyle: {
            color: '#fff',
            fontSize: 16,
            fontWeight: 'bold',
          },
        },
      },
      // itemStyle: {
      //   normal: {
      //     areaColor: {
      //       type: 'linear',
      //       x: 1200,
      //       y: 0,
      //       x2: 0,
      //       y2: 0,
      //       colorStops: [
      //         {
      //           offset: 0,
      //           color: 'rgba(3,27,78,0.75)', // 0% 处的颜色
      //         },
      //         {
      //           offset: 1,
      //           color: 'rgba(58,149,253,0.75)', // 50% 处的颜色
      //         },
      //       ],
      //       global: true, // 缺省为 false
      //     },
      //     borderColor: '#c0f3fb',
      //     borderWidth: 1,
      //     shadowColor: '#8cd3ef',
      //     shadowOffsetY: 10,
      //     shadowBlur: 120,
      //   },
      //   emphasis: {
      //     areaColor: 'rgba(0,254,233,0.6)',
      //   },
      // },
    },
    {
      type: 'map',
      map: 'guangxi',
      zlevel: -1,
      aspectScale: 1,
      zoom: 0.65,
      layoutCenter: ['50%', '51%'],
      layoutSize: '180%',
      roam: false,
      silent: true,
      itemStyle: {
        normal: {
          borderWidth: 1,
          // borderColor:"rgba(17, 149, 216,0.6)",
          borderColor: 'rgba(58,149,253,0.8)',
          shadowColor: 'rgba(172, 122, 255,0.5)',
          shadowOffsetY: 5,
          shadowBlur: 15,
          areaColor: 'rgba(5,21,35,0.1)',
        },
      },
    },
    {
      type: 'map',
      map: 'guangxi',
      zlevel: -2,
      aspectScale: 1,
      zoom: 0.65,
      layoutCenter: ['50%', '52%'],
      layoutSize: '180%',
      roam: false,
      silent: true,
      itemStyle: {
        normal: {
          borderWidth: 1,
          // borderColor: "rgba(57, 132, 188,0.4)",
          borderColor: 'rgba(58,149,253,0.6)',
          shadowColor: 'rgba(65, 214, 255,1)',
          shadowOffsetY: 5,
          shadowBlur: 15,
          areaColor: 'transpercent',
        },
      },
    },
    {
      type: 'map',
      map: 'guangxi',
      zlevel: -3,
      aspectScale: 1,
      zoom: 0.65,
      layoutCenter: ['50%', '53%'],
      layoutSize: '180%',
      roam: false,
      silent: true,
      itemStyle: {
        normal: {
          borderWidth: 1,
          // borderColor: "rgba(11, 43, 97,0.8)",
          borderColor: 'rgba(58,149,253,0.4)',
          shadowColor: 'rgba(58,149,253,1)',
          shadowOffsetY: 15,
          shadowBlur: 10,
          areaColor: 'transpercent',
        },
      },
    },
    {
      type: 'map',
      map: 'guangxi',
      zlevel: -4,
      aspectScale: 1,
      zoom: 0.65,
      layoutCenter: ['50%', '54%'],
      layoutSize: '180%',
      roam: false,
      silent: true,
      itemStyle: {
        normal: {
          borderWidth: 5,
          // borderColor: "rgba(11, 43, 97,0.8)",
          borderColor: 'rgba(5,9,57,0.8)',
          shadowColor: 'rgba(29, 111, 165,0.8)',
          shadowOffsetY: 15,
          shadowBlur: 10,
          areaColor: 'rgba(5,21,35,0.1)',
        },
      },
    },
  ],
  series: [
    {
      type: 'map',
      map: 'guangxi',
      geoIndex: 0,
      aspectScale: 1, //长宽比
      zoom: 0.65,
      showLegendSymbol: true,
      roam: true,
      label: {
        normal: {
          show: true,
          textStyle: {
            color: '#fff',
            fontSize: '120%',
            fontWeight: 'bold',
            textShadowColor: 'rgba(0, 0, 0, 0.5)',
            textShadowBlur: 5,
            textShadowOffsetX: 1,
            textShadowOffsetY: 1,
          },
        },
        emphasis: {
          show: true,
          textStyle: {
            color: '#fff',
            fontSize: '140%',
            fontWeight: 'bold',
          },
        },
      },
      itemStyle: {
        normal: {
          areaColor: {
            type: 'linear',
            x: 1200,
            y: 0,
            x2: 0,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(3,27,78,0.75)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(58,149,253,0.75)', // 50% 处的颜色
              },
            ],
            global: true, // 缺省为 false
          },
          borderColor: '#fff',
          borderWidth: 0.2,
        },
      },
      layoutCenter: ['50%', '50%'],
      layoutSize: '180%',
      animation: false,
      markPoint: {
        symbol: 'none',
      },
      data: [],
    },
  ],
})

// 地图数据状态
const mapDataState = reactive({
  // 人员总数
  totalCount: 0,
  // 各类案件人员数
  caseCounts: {
    borderViolation: 0, // 妨害国边境
    smuggling: 0, // 走私
    drugRelated: 0, // 涉毒
    weaponRelated: 0, // 涉枪
  },
  // 各地市数据
  cityData: [],
  // 是否正在加载数据
  loading: false,
})

// 获取地图数据
const fetchMapData = async () => {
  // 只有在钻取模式下才请求数据
  if (mapStack.mode !== 'drill') return

  try {
    console.log('获取地图数据，当前级别:', mapStack.level, '地区代码:', queryStore.dept)
    mapDataState.loading = true
    const data = await getRyzsData(queryStore.dept || '45')
    console.log('获取到的数据:', data)

    // 更新总数和案件分类数据
    mapDataState.totalCount = data.totalCount || 0
    mapDataState.caseCounts = {
      borderViolation: data.borderViolation || 0,
      smuggling: data.smuggling || 0,
      drugRelated: data.drugRelated || 0,
      weaponRelated: data.weaponRelated || 0,
    }

    // 处理地市数据并排序
    if (data.cityData && Array.isArray(data.cityData)) {
      mapDataState.cityData = data.cityData.sort((a, b) => b.value - a.value)

      // 更新地图样式
      updateMapColorByRanking()
    }
  } catch (error) {
    console.error('获取地图数据失败:', error)
  } finally {
    mapDataState.loading = false
  }
}

// 修改updateMapColorByRanking方法，适应单独配置
const updateMapColorByRanking = () => {
  if (!mapDataState.cityData.length) return

  // 只有在钻取模式下才处理数据
  if (mapStack.mode !== 'drill') return

  // 不同级别的地图处理方式可能不同
  if (mapStack.level !== 1) {
    // 处理第二级地图数据
    if (
      mapDataState.cityData.length > 0 &&
      mapOption.value.series &&
      mapOption.value.series.length > 0
    ) {
      // 加载地图数据
      import(`@/views/mapdata/${mapStack.code}.json`)
        .then((module) => {
          const mapData = module.default || module

          // 获取地图中所有区县的名称
          const mapFeatures = mapData.features || []

          // 为每个区县创建地图数据
          const countyData = mapFeatures.map((feature) => {
            const countyName = feature.properties?.name || ''

            // 查找匹配的数据，使用模糊匹配
            const matchedData = mapDataState.cityData.find((city) => {
              // 模糊匹配：只要地图名称包含接口返回的名称，或接口返回的名称包含地图名称，就视为匹配
              return countyName.includes(city.name) || city.name.includes(countyName)
            })

            return {
              name: countyName,
              value: matchedData?.value || 0,
              detail: matchedData?.detail || {
                total: 0,
                borderViolation: 0,
                smuggling: 0,
                drugRelated: 0,
                weaponRelated: 0,
              },
            }
          })

          // 更新地图数据
          mapOption.value.series[0].data = countyData
        })
        .catch((error) => {
          console.error('加载地图数据失败:', error)
        })
    }
    return
  }

  // 获取地市排名
  const sortedCities = [...mapDataState.cityData].sort((a, b) => b.value - a.value)
  const totalCities = sortedCities.length

  // 为每个城市增加一个排名属性
  const rankedCities = sortedCities.map((city, index) => ({
    ...city,
    rank: index + 1, // 排名从1开始
  }))

  // 获取第5名和倒数第3名的城市
  const rank5City = rankedCities[4] || rankedCities[rankedCities.length - 1]
  const rankLastThird = rankedCities[totalCities - 3] || rankedCities[0]

  // 为了处理重复值的情况，我们使用rank属性来确保分界点的唯一性
  // 前5名的分界值，使用第5名的值再减一点点，确保第5名被包含
  const highValue = rank5City ? rank5City.value - 0.001 : 0

  // 后3名的分界值，使用倒数第3名的值再加一点点，确保倒数第3名被包含
  const lowValue = rankLastThird ? rankLastThird.value + 0.001 : 0

  // 创建地图数据，保持原始值但给每个城市添加排名信息
  const cityData = rankedCities.map((city) => ({
    name: city.name,
    value: city.value,
    rank: city.rank,
    // 使用每个地市的详细数据
    detail: city.detail,
    // 映射区域
    visualMap: city.rank <= 5 ? 2 : city.rank > totalCities - 3 ? 0 : 1,
  }))

  // 更新visualMap配置
  if (mapOption.value.visualMap) {
    // 设置visualMap分段
    mapOption.value.visualMap.pieces = [
      { max: lowValue, label: '后3名', color: '#ff5757' }, // 红色
      { min: lowValue, max: highValue, label: '中间', color: '#ffad33' }, // 橙色
      { min: highValue, label: '前5名', color: '#19b76b' }, // 绿色
    ]
  }

  // 更新数据
  if (mapOption.value.series && mapOption.value.series.length > 0) {
    mapOption.value.series[0].data = cityData
  }
}

// 修改tooltip的formatter
const createDrillLevelOneMapOption = (code) => {
  return {
    backgroundColor: '#001829',
    title: {
      text: '',
      subtext: '',
      left: 'center',
      top: 10,
      textStyle: {
        color: '#fff',
        fontSize: 24,
        fontWeight: 'bold',
      },
      subtextStyle: {
        color: '#ccc',
        fontSize: 14,
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        if (!params.data) return params.name
        const detail = params.data.detail
        return `
          <div style="font-weight:bold;font-size:16px;margin-bottom:5px;">${params.name}</div>
          <div style="display:flex;justify-content:space-between;margin:3px 0;">
            <span>总人数:</span>
            <span style="font-weight:bold;color:#ffad33;">${detail.total || 0}人</span>
          </div>
          <div style="display:flex;justify-content:space-between;margin:3px 0;">
            <span>妨害国边境:</span>
            <span style="font-weight:bold;color:#5ac8fa;">${detail.borderViolation || 0}人</span>
          </div>
          <div style="display:flex;justify-content:space-between;margin:3px 0;">
            <span>走私:</span>
            <span style="font-weight:bold;color:#5ac8fa;">${detail.smuggling || 0}人</span>
          </div>
          <div style="display:flex;justify-content:space-between;margin:3px 0;">
            <span>涉毒:</span>
            <span style="font-weight:bold;color:#5ac8fa;">${detail.drugRelated || 0}人</span>
          </div>
          <div style="display:flex;justify-content:space-between;margin:3px 0;">
            <span>涉枪:</span>
            <span style="font-weight:bold;color:#5ac8fa;">${detail.weaponRelated || 0}人</span>
          </div>
        `
      },
      backgroundColor: 'rgba(0,24,41,0.9)',
      borderColor: '#5ac8fa',
      borderWidth: 1,
      padding: 10,
      textStyle: {
        color: '#fff',
        fontSize: 14,
      },
    },
    visualMap: {
      show: true,
      type: 'piecewise',
      right: 30,
      bottom: 30,
      pieces: [
        { min: 0, label: '前5名', color: '#19b76b' }, // 绿色
        { min: 0, label: '中间', color: '#ffad33' }, // 橙色
        { min: 0, label: '后3名', color: '#ff5757' }, // 红色
      ],
      textStyle: {
        color: '#fff',
      },
    },
    geo: [
      {
        layoutCenter: ['50%', '50%'],
        layoutSize: '180%',
        show: true,
        map: code,
        roam: false,
        zoom: 0.65,
        aspectScale: 1,
        label: {
          normal: {
            show: true,
            textStyle: {
              color: '#fff',
              fontSize: 14,
              fontWeight: 'bold',
              textShadowColor: 'rgba(0, 0, 0, 0.5)',
              textShadowBlur: 5,
              textShadowOffsetX: 1,
              textShadowOffsetY: 1,
            },
          },
          emphasis: {
            show: true,
            textStyle: {
              color: '#fff',
              fontSize: 16,
              fontWeight: 'bold',
            },
          },
        },
      },
      {
        type: 'map',
        map: code,
        zlevel: -1,
        aspectScale: 1,
        zoom: 0.65,
        layoutCenter: ['50%', '51%'],
        layoutSize: '180%',
        roam: false,
        silent: true,
        itemStyle: {
          normal: {
            borderWidth: 1,
            borderColor: 'rgba(58,149,253,0.8)',
            shadowColor: 'rgba(172, 122, 255,0.5)',
            shadowOffsetY: 5,
            shadowBlur: 15,
            areaColor: 'rgba(5,21,35,0.1)',
          },
        },
      },
      {
        type: 'map',
        map: code,
        zlevel: -2,
        aspectScale: 1,
        zoom: 0.65,
        layoutCenter: ['50%', '52%'],
        layoutSize: '180%',
        roam: false,
        silent: true,
        itemStyle: {
          normal: {
            borderWidth: 1,
            borderColor: 'rgba(58,149,253,0.6)',
            shadowColor: 'rgba(65, 214, 255,1)',
            shadowOffsetY: 5,
            shadowBlur: 15,
            areaColor: 'transpercent',
          },
        },
      },
      {
        type: 'map',
        map: code,
        zlevel: -3,
        aspectScale: 1,
        zoom: 0.65,
        layoutCenter: ['50%', '53%'],
        layoutSize: '180%',
        roam: false,
        silent: true,
        itemStyle: {
          normal: {
            borderWidth: 1,
            borderColor: 'rgba(58,149,253,0.4)',
            shadowColor: 'rgba(58,149,253,1)',
            shadowOffsetY: 15,
            shadowBlur: 10,
            areaColor: 'transpercent',
          },
        },
      },
      {
        type: 'map',
        map: code,
        zlevel: -4,
        aspectScale: 1,
        zoom: 0.65,
        layoutCenter: ['50%', '54%'],
        layoutSize: '180%',
        roam: false,
        silent: true,
        itemStyle: {
          normal: {
            borderWidth: 5,
            borderColor: 'rgba(5,9,57,0.8)',
            shadowColor: 'rgba(29, 111, 165,0.8)',
            shadowOffsetY: 15,
            shadowBlur: 10,
            areaColor: 'rgba(5,21,35,0.1)',
          },
        },
      },
    ],
    series: [
      {
        type: 'map',
        map: code,
        geoIndex: 0,
        aspectScale: 1,
        zoom: 0.65,
        showLegendSymbol: true,
        roam: true,
        label: {
          normal: {
            show: true,
            textStyle: {
              color: '#fff',
              fontSize: '120%',
              fontWeight: 'bold',
              textShadowColor: 'rgba(0, 0, 0, 0.5)',
              textShadowBlur: 5,
              textShadowOffsetX: 1,
              textShadowOffsetY: 1,
            },
          },
          emphasis: {
            show: true,
            textStyle: {
              color: '#fff',
              fontSize: '140%',
              fontWeight: 'bold',
            },
          },
        },
        itemStyle: {
          normal: {
            borderColor: '#fff',
            borderWidth: 0.5,
          },
          emphasis: {
            areaColor: '#00fefe',
            borderColor: '#fff',
            borderWidth: 1,
          },
        },
        layoutCenter: ['50%', '50%'],
        layoutSize: '180%',
        animation: false,
        markPoint: {
          symbol: 'none',
        },
        data: [],
      },
    ],
  }
}

// 创建通用地图配置（用于其他情况）
const createCommonMapOption = (code) => {
  return {
    backgroundColor: '#001829',
    title: {
      text: '',
      subtext: '',
      left: 'center',
      top: 10,
      textStyle: {
        color: '#fff',
        fontSize: 24,
        fontWeight: 'bold',
      },
      subtextStyle: {
        color: '#ccc',
        fontSize: 14,
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        if (!params.data) return params.name
        const detail = params.data.detail
        return `
          <div style="font-weight:bold;font-size:16px;margin-bottom:5px;">${params.name}</div>
          <div style="display:flex;justify-content:space-between;margin:3px 0;">
            <span>总人数:</span>
            <span style="font-weight:bold;color:#ffad33;">${detail?.total || 0}人</span>
          </div>
          <div style="display:flex;justify-content:space-between;margin:3px 0;">
            <span>妨害国边境:</span>
            <span style="font-weight:bold;color:#5ac8fa;">${detail?.borderViolation || 0}人</span>
          </div>
          <div style="display:flex;justify-content:space-between;margin:3px 0;">
            <span>走私:</span>
            <span style="font-weight:bold;color:#5ac8fa;">${detail?.smuggling || 0}人</span>
          </div>
          <div style="display:flex;justify-content:space-between;margin:3px 0;">
            <span>涉毒:</span>
            <span style="font-weight:bold;color:#5ac8fa;">${detail?.drugRelated || 0}人</span>
          </div>
          <div style="display:flex;justify-content:space-between;margin:3px 0;">
            <span>涉枪:</span>
            <span style="font-weight:bold;color:#5ac8fa;">${detail?.weaponRelated || 0}人</span>
          </div>
        `
      },
      backgroundColor: 'rgba(0,24,41,0.9)',
      borderColor: '#5ac8fa',
      borderWidth: 1,
      padding: 10,
      textStyle: {
        color: '#fff',
        fontSize: 14,
      },
      extraCssText: 'width: 220px;',
    },
    geo: [
      {
        layoutCenter: ['50%', '50%'],
        layoutSize: '180%',
        show: true,
        map: code,
        roam: false,
        zoom: 0.65,
        aspectScale: 1,
        label: {
          normal: {
            show: true,
            textStyle: {
              color: '#fff',
              fontSize: 14,
              fontWeight: 'bold',
              textShadowColor: 'rgba(0, 0, 0, 0.5)',
              textShadowBlur: 5,
              textShadowOffsetX: 1,
              textShadowOffsetY: 1,
            },
          },
          emphasis: {
            show: true,
            textStyle: {
              color: '#fff',
              fontSize: 16,
              fontWeight: 'bold',
            },
          },
        },
        itemStyle: {
          normal: {
            areaColor: {
              type: 'linear',
              x: 1200,
              y: 0,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(3,27,78,0.75)', // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: 'rgba(58,149,253,0.75)', // 50% 处的颜色
                },
              ],
              global: true, // 缺省为 false
            },
            borderColor: '#c0f3fb',
            borderWidth: 1,
            shadowColor: '#8cd3ef',
            shadowOffsetY: 10,
            shadowBlur: 120,
          },
          emphasis: {
            areaColor: 'rgba(0,254,233,0.6)',
          },
        },
      },
      {
        type: 'map',
        map: code,
        zlevel: -1,
        aspectScale: 1,
        zoom: 0.65,
        layoutCenter: ['50%', '51%'],
        layoutSize: '180%',
        roam: false,
        silent: true,
        itemStyle: {
          normal: {
            borderWidth: 1,
            borderColor: 'rgba(58,149,253,0.8)',
            shadowColor: 'rgba(172, 122, 255,0.5)',
            shadowOffsetY: 5,
            shadowBlur: 15,
            areaColor: 'rgba(5,21,35,0.1)',
          },
        },
      },
      {
        type: 'map',
        map: code,
        zlevel: -2,
        aspectScale: 1,
        zoom: 0.65,
        layoutCenter: ['50%', '52%'],
        layoutSize: '180%',
        roam: false,
        silent: true,
        itemStyle: {
          normal: {
            borderWidth: 1,
            borderColor: 'rgba(58,149,253,0.6)',
            shadowColor: 'rgba(65, 214, 255,1)',
            shadowOffsetY: 5,
            shadowBlur: 15,
            areaColor: 'transpercent',
          },
        },
      },
      {
        type: 'map',
        map: code,
        zlevel: -3,
        aspectScale: 1,
        zoom: 0.65,
        layoutCenter: ['50%', '53%'],
        layoutSize: '180%',
        roam: false,
        silent: true,
        itemStyle: {
          normal: {
            borderWidth: 1,
            borderColor: 'rgba(58,149,253,0.4)',
            shadowColor: 'rgba(58,149,253,1)',
            shadowOffsetY: 15,
            shadowBlur: 10,
            areaColor: 'transpercent',
          },
        },
      },
      {
        type: 'map',
        map: code,
        zlevel: -4,
        aspectScale: 1,
        zoom: 0.65,
        layoutCenter: ['50%', '54%'],
        layoutSize: '180%',
        roam: false,
        silent: true,
        itemStyle: {
          normal: {
            borderWidth: 5,
            borderColor: 'rgba(5,9,57,0.8)',
            shadowColor: 'rgba(29, 111, 165,0.8)',
            shadowOffsetY: 15,
            shadowBlur: 10,
            areaColor: 'rgba(5,21,35,0.1)',
          },
        },
      },
    ],
    series: [
      {
        type: 'map',
        map: code,
        geoIndex: 0,
        aspectScale: 1,
        zoom: 0.65,
        showLegendSymbol: true,
        roam: true,
        label: {
          normal: {
            show: true,
            textStyle: {
              color: '#fff',
              fontSize: '120%',
              fontWeight: 'bold',
              textShadowColor: 'rgba(0, 0, 0, 0.5)',
              textShadowBlur: 5,
              textShadowOffsetX: 1,
              textShadowOffsetY: 1,
            },
          },
          emphasis: {
            show: true,
            textStyle: {
              color: '#fff',
              fontSize: '140%',
              fontWeight: 'bold',
            },
          },
        },
        itemStyle: {
          normal: {
            areaColor: {
              type: 'linear',
              x: 1200,
              y: 0,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(3,27,78,0.75)', // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: 'rgba(58,149,253,0.75)', // 50% 处的颜色
                },
              ],
              global: true, // 缺省为 false
            },
            borderColor: '#fff',
            borderWidth: 0.2,
          },
        },
        layoutCenter: ['50%', '50%'],
        layoutSize: '180%',
        animation: false,
        markPoint: {
          symbol: 'none',
        },
        data: [],
      },
    ],
  }
}

// 更新多选模式下的地图样式
const updateMultiSelectMapStyle = () => {
  // 更新地图系列的选中状态样式
  if (mapOption.value.series && mapOption.value.series.length > 0) {
    // 更新地图数据
    const cityData = Object.keys(regionMap)
      .filter((key) => key !== '4500')
      .map((code) => {
        const isSelected = mapStack.selectedCities.includes(code)
        // 从 mapDataState 中获取该城市的详细数据
        const cityDetail = mapDataState.cityData.find((city) => city.name === regionMap[code])
        return {
          name: regionMap[code],
          value: isSelected ? 80 : 50,
          detail: cityDetail?.detail || {
            total: 0,
            borderViolation: 0,
            smuggling: 0,
            drugRelated: 0,
            weaponRelated: 0,
          },
          itemStyle: isSelected
            ? {
                areaColor: 'rgba(0,254,233,0.6)',
                borderColor: '#00fefe',
                borderWidth: 2,
              }
            : null,
        }
      })

    mapOption.value.series[0].data = cityData
  }
}

// 地图点击事件处理
const handleMapClick = (params) => {
  console.log('params', params)
  if (!params.name) return

  // 多选模式下的点击处理
  if (mapStack.mode === 'multiSelect' && mapStack.level === 1) {
    // 多选模式只在广西层级生效
    const cityCode = Object.keys(regionMap).find((key) => regionMap[key] === params.name)
    if (!cityCode || cityCode === '4500') return // 不处理广西整体的点击

    // 切换选中状态
    const index = mapStack.selectedCities.indexOf(cityCode)
    if (index === -1) {
      // 选中
      mapStack.selectedCities.push(cityCode)
    } else {
      // 取消选中
      mapStack.selectedCities.splice(index, 1)
    }

    // 更新地图样式
    updateMultiSelectMapStyle()

    // 更新查询条件
    if (mapStack.selectedCities.length > 0) {
      queryStore.setDept(mapStack.selectedCities.join(','))
    } else {
      queryStore.setDept('45') // 没有选中则默认为广西整体
    }

    return
  }

  // 钻取模式下的点击处理
  if (mapStack.mode === 'drill') {
    if (mapStack.level === 1) {
      // 点击地级市，进入第二级
      const cityCode = Object.keys(regionMap).find((key) => regionMap[key] === params.name)
      if (!cityCode) return

      // 保存历史记录
      mapStack.history.push({
        level: mapStack.level,
        code: mapStack.code,
        name: mapStack.name,
      })

      // 更新层级
      mapStack.level = 2

      // 更新查询条件
      queryStore.setDept(cityCode)

      // 加载地市地图
      loadMapData(cityCode)
    } else if (mapStack.level === 2) {
      console.log('mapStack', mapStack)
      // 在第二级点击，获取区县代码
      if (params.componentType === 'series' && params.name) {
        // 从地图数据中查找该区县的代码
        try {
          // 找到当前加载的地图数据
          import(`@/views/mapdata/${mapStack.code}.json`)
            .then((module) => {
              const mapData = module.default || module
              // 在地图数据中查找匹配的区县
              const county = mapData.features.find(
                (feature) => feature.properties && feature.properties.name === params.name,
              )

              if (county && county.properties && county.properties.adcode) {
                // 获取到区县代码
                const countyCode = county.properties.adcode
                console.log('区县代码:', countyCode, '区县名称:', params.name)

                // 使用 queryStore 保存选中的区县代码（如果需要）
                queryStore.setDept(countyCode)

                // 这里可以添加其他处理逻辑，比如进入第三级地图等
              } else {
                console.warn('未找到区县代码:', params.name)
              }
            })
            .catch((error) => {
              console.error('获取区县代码失败:', error)
            })
        } catch (error) {
          console.error('处理区县点击事件失败:', error)
        }
        return
      }

      // 如果点击空白处，返回上一级
      if (params.componentType === 'series' && !params.name) {
        goBack()
      }
    }
  }
}

// 返回上一级
const goBack = () => {
  if (mapStack.history.length === 0) return

  // 取出上一级信息
  const prev = mapStack.history.pop()
  mapStack.level = prev.level

  // 加载上一级地图
  loadMapData(prev.code)
  const code = prev.code === '4500' ? '45' : prev.code
  queryStore.setDept(code)
}

// 从多选中移除指定城市
const removeSelectedCity = (code) => {
  const index = mapStack.selectedCities.indexOf(code)
  if (index !== -1) {
    mapStack.selectedCities.splice(index, 1)

    // 更新地图样式
    updateMultiSelectMapStyle()

    // 更新查询条件
    if (mapStack.selectedCities.length > 0) {
      queryStore.setDept(mapStack.selectedCities.join(','))
    } else {
      queryStore.setDept('45') // 没有选中则默认为广西整体
    }
  }
}

// 修改loadMapData方法，根据不同条件使用不同配置
const loadMapData = async (code) => {
  try {
    // 动态导入地图数据
    const module = await import(`@/views/mapdata/${code}.json`)
    const mapData = module.default || module

    // 注册地图
    echarts.registerMap(code, mapData)

    // 更新当前地区信息
    mapStack.code = code
    mapStack.name = regionMap[code] || code

    // 根据条件选择合适的地图配置
    if (mapStack.level === 1 && mapStack.mode === 'drill') {
      // 第一级且为钻取模式，使用特殊配置
      mapOption.value = createDrillLevelOneMapOption(code)
    } else {
      // 其他情况，使用通用配置
      console.log('非钻取模式')
      mapOption.value = createCommonMapOption(code)
    }

    // 准备数据
    if (mapStack.mode === 'multiSelect' && mapStack.level === 1) {
      // 如果是多选模式，应用选中的城市样式
      updateMultiSelectMapStyle()
    } else if (mapStack.mode === 'drill') {
      // 如果是钻取模式，无论是第一级还是第二级，都获取地图数据
      fetchMapData()
    }
  } catch (error) {
    console.error('加载地图数据失败:', error)
  }
}

// 修改toggleMapMode方法，切换模式时重新加载地图配置
const toggleMapMode = (mode) => {
  if (mapStack.mode === mode) return

  // 切换到新模式
  const oldMode = mapStack.mode
  mapStack.mode = mode

  // 同步模式到全局查询条件store
  queryStore.setMapMode(mode)

  // 如果从多选模式切换到钻取模式，清空已选城市
  if (mode === 'drill') {
    mapStack.selectedCities = []
    // 如果当前不在广西层级，返回广西层级
    if (mapStack.level !== 1) {
      mapStack.level = 1
      mapStack.history = []
      console.log('切换到广西层级')
      loadMapData('4500')
      queryStore.setDept('45')
      // 在切换到第一级时获取地图数据
      fetchMapData()
    } else {
      // 即使在广西层级，也需要重新设置查询参数并刷新
      console.log('已在广西层级，重新设置请求参数')
      queryStore.setDept('45')

      // 如果是从其他模式切换到钻取模式，需要重新加载带有visualMap的配置
      if (oldMode !== 'drill') {
        loadMapData('4500')
      } else {
        // 如果本身就是钻取模式，只刷新数据
        fetchMapData()
      }
    }
  } else if (mode === 'multiSelect') {
    console.log('chartRef', chartRef.value)
    chartRef.value.clear()
    // 如果当前不在广西层级，返回广西层级
    if (mapStack.level !== 1) {
      mapStack.level = 1
      mapStack.history = []

      loadMapData('4500')
      queryStore.setDept('45')
    } else if (oldMode === 'drill') {
      // 如果是从钻取模式切换到多选模式，需要重新加载不带visualMap的配置
      loadMapData('4500')
    } else {
      // 如果已经在广西层级，只需更新地图样式
      updateMultiSelectMapStyle()
    }
  }
}

// 监听查询条件变化
queryStore.$subscribe(() => {
  console.log('queryStore条件变化，获取新数据')
  // 当查询条件变化时重新加载地图数据
  fetchMapData()
})

// 初始化
onMounted(() => {
  // 初始化导航配置
  initNavConfig()

  // 确保queryStore中的mapMode与当前mapStack.mode一致
  queryStore.setMapMode(mapStack.mode)

  // 加载广西地图
  loadMapData('4500')

  // 监听地图点击事件
  if (chartRef.value) {
    const chart = chartRef.value.chart
    chart.on('click', handleMapClick)
  }

  // 获取地图数据
  fetchMapData()
})

// 监听queryStore中的resetMapLevel标志
watch(
  () => queryStore.resetMapLevel,
  (newVal) => {
    if (newVal) {
      // 重置地图到第一级
      mapStack.level = 1
      mapStack.history = []
      mapStack.code = '4500'
      mapStack.name = '广西'
      mapStack.selectedCities = []

      // 重新加载广西地图
      loadMapData('4500')

      // 获取地图数据
      fetchMapData()

      // 清除重置标志
      queryStore.resetMapLevel = false
    }
  },
)
</script>

<template>
  <div class="map-control-container">
    <!-- 顶部导航栏 -->
    <div class="nav-bar">
      <div
        v-for="item in navItems"
        :key="item.id"
        class="nav-item"
        :class="{ active: activeNavItem === item.id }"
        @click="handleNavClick(item.id)"
      >
        <img class="nav-icon" :src="item.icon" :alt="item.name" />
        <div class="nav-text">{{ item.name }}</div>
      </div>
    </div>

    <div class="map-wrapper">
      <v-chart ref="chartRef" class="chart" :option="mapOption" />
    </div>

    <!-- 地图模式切换 -->
    <div class="map-mode-switch">
      <div class="switch-label">地图模式：</div>
      <div class="switch-options">
        <div
          class="switch-option"
          :class="{ active: mapStack.mode === 'drill' }"
          @click="toggleMapMode('drill')"
        >
          钻取模式
        </div>
        <div
          class="switch-option"
          :class="{ active: mapStack.mode === 'multiSelect' }"
          @click="toggleMapMode('multiSelect')"
        >
          多选模式
        </div>
      </div>
    </div>

    <!-- 多选模式下的已选地市 -->
    <div
      v-if="mapStack.mode === 'multiSelect' && mapStack.selectedCities.length > 0"
      class="selected-cities"
    >
      <div class="selected-label">已选择：</div>
      <div class="selected-list">
        <div v-for="code in mapStack.selectedCities" :key="code" class="selected-city">
          {{ regionMap[code] }}
          <span class="remove-city" @click.stop="removeSelectedCity(code)">×</span>
        </div>
      </div>
    </div>

    <!-- 返回按钮 -->
    <div v-if="mapStack.level > 1" class="back-button" @click="goBack">
      返回{{ mapStack.history[mapStack.history.length - 1]?.name || '上一级' }}
    </div>
  </div>
</template>

<style scoped>
.map-control-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

/* 顶部导航栏样式 */
.nav-bar {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: space-around;
  width: 800px;
  height: 60px;
  border-radius: 10px;
  z-index: 100;
  padding: 5px;
}

.nav-item {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  width: 160px;
  height: 58px;
  margin: 0 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #fff;
  padding: 0 15px;
  position: relative;
}

/* .nav-item:hover {
  background-color: rgba(90, 200, 250, 0.3);
}

.nav-item.active {
  background-color: rgba(90, 200, 250, 0.4);
  box-shadow: 0 0 15px rgba(90, 200, 250, 0.5);
} */

.nav-icon {
  width: 144px;
  height: 58px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}

.nav-text {
  font-family: PangMenZhengDao;
  font-size: 20px;
  color: #e5faff;
  letter-spacing: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  font-weight: 400;
  padding-left: 48px;
}

.map-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart {
  width: 100%;
  height: 100%;
}

.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  padding: 8px 16px;
  background: rgba(0, 24, 41, 0.8);
  border: 1px solid #5ac8fa;
  border-radius: 4px;
  color: #fff;
  font-size: 14px;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 0 10px rgba(90, 200, 250, 0.5);
  transition: all 0.3s;
}

.back-button:hover {
  background: rgba(0, 36, 61, 0.9);
  box-shadow: 0 0 15px rgba(90, 200, 250, 0.7);
}

/* 地图模式切换样式 */
.map-mode-switch {
  position: absolute;
  bottom: 20px;
  left: 30px;
  background: rgba(0, 24, 41, 0.8);
  border: 1px solid #5ac8fa;
  border-radius: 4px;
  color: #fff;
  display: flex;
  flex-direction: column;
  padding: 8px;
  z-index: 10;
  box-shadow: 0 0 10px rgba(90, 200, 250, 0.5);
}

.switch-label {
  font-size: 14px;
  margin-bottom: 6px;
  text-align: center;
}

.switch-options {
  display: flex;
  justify-content: space-between;
}

.switch-option {
  padding: 5px 10px;
  cursor: pointer;
  border-radius: 3px;
  transition: all 0.3s;
  margin: 0 3px;
}

.switch-option:hover {
  background: rgba(90, 200, 250, 0.3);
}

.switch-option.active {
  background: rgba(90, 200, 250, 0.5);
  font-weight: bold;
}

/* 已选城市样式 */
.selected-cities {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 24, 41, 0.8);
  border: 1px solid #5ac8fa;
  border-radius: 4px;
  color: #fff;
  padding: 8px;
  z-index: 10;
  box-shadow: 0 0 10px rgba(90, 200, 250, 0.5);
  max-width: 300px;
}

.selected-label {
  font-size: 14px;
  margin-bottom: 6px;
}

.selected-list {
  display: flex;
  flex-wrap: wrap;
}

.selected-city {
  background: rgba(90, 200, 250, 0.3);
  padding: 3px 8px;
  border-radius: 3px;
  margin: 2px;
  font-size: 13px;
  display: flex;
  align-items: center;
}

.remove-city {
  margin-left: 5px;
  font-weight: bold;
  cursor: pointer;
  font-size: 14px;
}

.remove-city:hover {
  color: #ff5252;
}
</style>
