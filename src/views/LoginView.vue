<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="bg-decoration">
      <div class="bg-pattern"></div>
      <div class="bg-overlay"></div>
    </div>

    <!-- 登录表单容器 -->
    <div class="login-form-container">
      <!-- 标题区域 -->
      <div class="title-section">
        <div class="main-title">边海防控驾驶舱</div>
        <div class="sub-title">BORDER CONTROL COMMAND CENTER</div>
        <div class="title-decoration"></div>
      </div>

      <!-- 登录表单 -->
      <div class="login-form">
        <div class="form-header">
          <div class="form-title">系统登录</div>
          <div class="form-subtitle">SYSTEM LOGIN</div>
        </div>

        <form @submit.prevent="handleLogin" class="form-content">
          <div class="input-group">
            <div class="input-label">用户名</div>
            <div class="input-wrapper">
              <input
                v-model="loginForm.username"
                type="text"
                placeholder="请输入用户用户名"
                class="form-input"
                required
              />
              <div class="input-icon">👤</div>
            </div>
          </div>

          <div class="input-group">
            <div class="input-label">密码</div>
            <div class="input-wrapper">
              <input
                v-model="loginForm.password"
                :type="showPassword ? 'text' : 'password'"
                placeholder="请输入密码"
                class="form-input"
                required
              />
              <div class="input-icon">🔒</div>
              <button type="button" class="password-toggle" @click="showPassword = !showPassword">
                {{ showPassword ? '👁️' : '👁️‍🗨️' }}
              </button>
            </div>
          </div>

          <div class="form-options">
            <label class="remember-me">
              <input type="checkbox" v-model="loginForm.remember" />
              <span>记住我</span>
            </label>
            <a href="#" class="forgot-password">忘记密码？</a>
          </div>

          <button type="submit" class="login-btn" :disabled="loading">
            <span v-if="!loading">登录</span>
            <span v-else class="loading-text">登录中...</span>
          </button>
        </form>

        <div class="form-footer">
          <div class="version-info">版本号：v1.0.0</div>
          <div class="copyright">© 2024 边海防控系统</div>
        </div>
      </div>
    </div>

    <!-- 右侧装饰区域 -->
    <!-- <div class="right-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
      <div class="decoration-line line-1"></div>
      <div class="decoration-line line-2"></div>
    </div> -->
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { auth } from '@/api'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const showPassword = ref(false)

const loginForm = reactive({
  username: '',
  password: '',
  remember: false,
})

// 登录处理
const handleLogin = async () => {
  if (!loginForm.username || !loginForm.password) {
    alert('请输入用户名和密码')
    return
  }

  loading.value = true

  try {
    // 调用真实的登录API
    const response = await auth.login({
      userid: loginForm.username,
      password: loginForm.password,
    })

    if (response) {
      // 登录成功，保存token和用户信息
      sessionStorage.setItem('access_token', response.loginUserDTO.access_token)
      sessionStorage.setItem('userInfo', JSON.stringify(response))
      // 跳转到首页
      router.push('/')
    } else {
      alert(response?.message || '登录失败')
    }
  } catch (error) {
    console.error('登录错误:', error)
    alert('登录失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0c1e35 0%, #1a3a5f 50%, #0c1e35 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(120, 200, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(120, 200, 255, 0.1) 0%, transparent 50%),
    linear-gradient(45deg, transparent 48%, rgba(120, 200, 255, 0.05) 50%, transparent 52%);
  background-size:
    100% 100%,
    100% 100%,
    50px 50px;
  animation: bgFloat 20s ease-in-out infinite;
}

.bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(12, 30, 53, 0.8) 0%, rgba(26, 58, 95, 0.6) 100%);
}

@keyframes bgFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(1deg);
  }
}

/* 登录表单容器 */
.login-form-container {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
}

/* 标题区域 */
.title-section {
  text-align: center;
  margin-bottom: 20px;
}

.main-title {
  font-size: 48px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 20px rgba(120, 200, 255, 0.5);
  margin-bottom: 10px;
  letter-spacing: 4px;
}

.sub-title {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.7);
  letter-spacing: 2px;
  margin-bottom: 20px;
}

.title-decoration {
  width: 200px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #78c8ff, transparent);
  margin: 0 auto;
  border-radius: 2px;
}

/* 登录表单 */
.login-form {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 40px;
  width: 400px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-title {
  font-size: 24px;
  color: #ffffff;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  letter-spacing: 1px;
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  width: 100%;
  height: 50px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  padding: 0 50px 0 20px;
  color: #ffffff;
  font-size: 16px;
  transition: all 0.3s ease;
}

.form-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-input:focus {
  outline: none;
  border-color: #78c8ff;
  box-shadow: 0 0 15px rgba(120, 200, 255, 0.3);
  background: rgba(255, 255, 255, 0.15);
}

.input-icon {
  position: absolute;
  right: 15px;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.6);
}

.password-toggle {
  position: absolute;
  right: 15px;
  background: none;
  border: none;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-toggle:hover {
  color: #78c8ff;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.remember-me input[type='checkbox'] {
  width: 16px;
  height: 16px;
  accent-color: #78c8ff;
}

.forgot-password {
  color: #78c8ff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #ffffff;
}

.login-btn {
  width: 100%;
  height: 50px;
  background: linear-gradient(135deg, #78c8ff 0%, #4a90e2 100%);
  border: none;
  border-radius: 10px;
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(120, 200, 255, 0.3);
}

.login-btn:active:not(:disabled) {
  transform: translateY(0);
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-text::after {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.form-footer {
  margin-top: 30px;
  text-align: center;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  line-height: 1.5;
}

/* 右侧装饰 */
.right-decoration {
  position: absolute;
  right: 0;
  top: 0;
  width: 300px;
  height: 100%;
  z-index: 5;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(120, 200, 255, 0.1) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 20%;
  right: -50px;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: -30px;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  top: 80%;
  right: -20px;
  animation-delay: 4s;
}

.decoration-line {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(120, 200, 255, 0.3), transparent);
  animation: glow 4s ease-in-out infinite;
}

.line-1 {
  width: 200px;
  height: 2px;
  top: 30%;
  right: 50px;
  transform: rotate(45deg);
  animation-delay: 1s;
}

.line-2 {
  width: 150px;
  height: 1px;
  top: 70%;
  right: 30px;
  transform: rotate(-30deg);
  animation-delay: 3s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes glow {
  0%,
  100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-form {
    width: 90%;
    max-width: 400px;
    padding: 30px 20px;
  }

  .main-title {
    font-size: 36px;
  }

  .right-decoration {
    display: none;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 28px;
  }

  .sub-title {
    font-size: 14px;
  }

  .login-form {
    padding: 20px 15px;
  }
}
</style>
