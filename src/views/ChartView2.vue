<script setup>
import { ref } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { <PERSON><PERSON><PERSON>, Pie<PERSON><PERSON> } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  MarkLineComponent,
  MarkPointComponent,
} from 'echarts/components'
import VChart from 'vue-echarts'

// 注册必须的组件
use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TitleComponent,
  Tooltip<PERSON>omponent,
  LegendComponent,
  GridComponent,
  MarkLineComponent,
  MarkPointComponent,
])

// 人员户籍地环形图配置
const getPieChartOption = (title) => {
  return {
    title: {
      text: title,
      left: 'center',
      top: '10px',
      textStyle: {
        color: '#FEDE4B',
        fontSize: 18,
        fontWeight: 'bold',
      },
    },
    legend: {
      orient: 'vertical',
      right: '5%',
      top: 'center',
      textStyle: {
        color: '#E5FAFF',
      },
      formatter: (name) => {
        const data = {
          广西省: 344,
          广东省: 263,
          云南省: 219,
          贵州省: 263,
          其他: 219,
        }
        return `${name}  ${data[name]}`
      },
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['30%', '55%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 0,
          borderColor: 'rgba(0, 0, 0, 0)',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '40',
            fontWeight: 'bold',
            color: '#E5FAFF',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 344, name: '广西省', itemStyle: { color: '#3C4EC2' } },
          { value: 263, name: '广东省', itemStyle: { color: '#4FDB93' } },
          { value: 219, name: '云南省', itemStyle: { color: '#FD625E' } },
          { value: 263, name: '贵州省', itemStyle: { color: '#FF8E3C' } },
          { value: 219, name: '其他', itemStyle: { color: '#E6E6E6' } },
        ],
      },
      {
        type: 'pie',
        radius: ['0%', '30%'],
        center: ['30%', '55%'],
        label: {
          position: 'center',
          formatter: '1,231\n总数',
          fontSize: 20,
          fontWeight: 'bold',
          color: '#E5FAFF',
          lineHeight: 24,
        },
        data: [
          {
            value: 1231,
            itemStyle: {
              color: 'rgba(0, 0, 0, 0)',
            },
          },
        ],
      },
    ],
  }
}

// 通行数据趋势图配置
const getTrendChartOption = () => {
  return {
    title: {
      text: '通行数据趋势',
      left: 'center',
      top: '10px',
      textStyle: {
        color: '#FEDE4B',
        fontSize: 18,
        fontWeight: 'bold',
      },
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '15%',
      top: '25%',
      containLabel: true,
    },
    legend: {
      data: ['近12个月', '前12个月'],
      textStyle: {
        color: '#fff',
      },
      icon: 'circle',
      itemWidth: 10,
      itemHeight: 10,
      right: '5%',
      top: '5%',
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      axisLine: {
        lineStyle: {
          color: '#fff',
          opacity: 0.2,
        },
      },
      axisLabel: {
        color: '#fff',
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 200,
      interval: 50,
      axisLine: {
        show: false,
      },
      axisLabel: {
        color: '#fff',
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
    },
    series: [
      {
        name: '近12个月',
        type: 'line',
        data: [150, 140, 150, 80, 80, 90, 120, 130, 70, 100, 110, 90],
        smooth: true,
        showSymbol: false,
        lineStyle: {
          width: 3,
          color: '#FF8E3C',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(255, 142, 60, 0.5)',
              },
              {
                offset: 1,
                color: 'rgba(255, 142, 60, 0.01)',
              },
            ],
          },
        },
        markPoint: {
          data: [
            {
              coord: [4, 80],
              symbol: 'circle',
              symbolSize: 10,
              itemStyle: {
                color: '#FF8E3C',
              },
            },
          ],
        },
      },
      {
        name: '前12个月',
        type: 'line',
        data: [70, 100, 40, 120, 140, 90, 100, 110, 80, 150, 80, 120],
        smooth: true,
        showSymbol: false,
        lineStyle: {
          width: 3,
          color: '#00C6FF',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(0, 198, 255, 0.5)',
              },
              {
                offset: 1,
                color: 'rgba(0, 198, 255, 0.01)',
              },
            ],
          },
        },
        markPoint: {
          data: [
            {
              coord: [4, 140],
              symbol: 'circle',
              symbolSize: 10,
              itemStyle: {
                color: '#00C6FF',
              },
            },
          ],
        },
      },
    ],
    markLine: {
      silent: true,
      data: [
        {
          xAxis: 4.5,
          lineStyle: {
            color: '#fff',
            opacity: 0.5,
            type: 'solid',
            width: 1,
          },
        },
      ],
    },
  }
}

// 自动调整图表
const autoresize = ref(true)
</script>

<template>
  <div class="dashboard-container">
    <!-- 上半部分 - 环形图统计 -->
    <div class="top-section">
      <div class="chart-content">
        <div class="chart-item">
          <v-chart
            :option="getPieChartOption('通行人员户籍地统计')"
            :autoresize="autoresize"
            class="chart"
          />
        </div>
        <div class="chart-item">
          <v-chart
            :option="getPieChartOption('通行车辆归属地统计')"
            :autoresize="autoresize"
            class="chart"
          />
        </div>
      </div>
    </div>

    <!-- 下半部分 - 趋势图和预警信息 -->
    <div class="bottom-section">
      <div class="chart-content">
        <!-- 左侧趋势图 -->
        <div class="chart-item trend-chart">
          <v-chart :option="getTrendChartOption()" :autoresize="autoresize" class="chart" />
        </div>

        <!-- 右侧预警信息 -->
        <div class="chart-item alert-panel">
          <div class="alert-title">预警信息统计</div>

          <!-- 预警信息第一行 -->
          <div class="alert-row">
            <div class="alert-item">
              <div class="alert-indicator red"></div>
              <div class="alert-text">红色预警</div>
              <div class="alert-number">9</div>
            </div>
            <div class="alert-item">
              <div class="alert-indicator orange"></div>
              <div class="alert-text">橙色预警</div>
              <div class="alert-number">9</div>
            </div>
            <div class="alert-item">
              <div class="alert-indicator yellow"></div>
              <div class="alert-text">黄色预警</div>
              <div class="alert-number">9</div>
            </div>
          </div>

          <!-- 预警信息第二行 -->
          <div class="alert-row">
            <div class="alert-item">
              <div class="alert-text small">便道卡口</div>
              <div class="alert-number">9</div>
            </div>
            <div class="alert-item">
              <div class="alert-text small">车道查验</div>
              <div class="alert-number">9</div>
            </div>
            <div class="alert-item">
              <div class="alert-text small">APP查验</div>
              <div class="alert-number">9</div>
            </div>
          </div>

          <!-- 预警信息第三行 -->
          <div class="alert-row">
            <div class="alert-item">
              <div class="alert-text small red-text">红色预警</div>
              <div class="alert-number">6</div>
            </div>
            <div class="alert-item">
              <div class="alert-text small red-text">红色预警</div>
              <div class="alert-number">6</div>
            </div>
            <div class="alert-item">
              <div class="alert-text small red-text">红色预警</div>
              <div class="alert-number">6</div>
            </div>
          </div>

          <!-- 预警信息第四行 -->
          <div class="alert-row">
            <div class="alert-item">
              <div class="alert-text small orange-text">橙色预警</div>
              <div class="alert-number">2</div>
            </div>
            <div class="alert-item">
              <div class="alert-text small orange-text">橙色预警</div>
              <div class="alert-number">2</div>
            </div>
            <div class="alert-item">
              <div class="alert-text small orange-text">橙色预警</div>
              <div class="alert-number">2</div>
            </div>
          </div>

          <!-- 预警信息第五行 -->
          <div class="alert-row">
            <div class="alert-item">
              <div class="alert-text small yellow-text">黄色预警</div>
              <div class="alert-number">1</div>
            </div>
            <div class="alert-item">
              <div class="alert-text small yellow-text">黄色预警</div>
              <div class="alert-number">1</div>
            </div>
            <div class="alert-item">
              <div class="alert-text small yellow-text">黄色预警</div>
              <div class="alert-number">1</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dashboard-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.5);
}

.top-section,
.bottom-section {
  width: 100%;
  height: 50%;
  display: flex;
}

.chart-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
}

.chart-item {
  flex: 1;
  height: 100%;
  position: relative;
  background-image: url('@/assets/dashboard/backgrounds/trend-bg.png');
  background-size: 100% 100%;
  padding: 10px;
}

.chart {
  width: 100%;
  height: 100%;
}

/* 预警信息样式 */
.alert-panel {
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.alert-title {
  color: #fede4b;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 20px;
}

.alert-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  height: 40px;
}

.alert-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: rgba(9, 24, 47, 0.6);
  margin: 0 5px;
  padding: 0 15px;
  border-radius: 4px;
}

.alert-item:nth-child(even) {
  background-color: rgba(16, 37, 68, 0.6);
}

.alert-indicator {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  margin-right: 8px;
}

.alert-indicator.red {
  background-color: #ff695a;
}

.alert-indicator.orange {
  background-color: #ff8e3c;
}

.alert-indicator.yellow {
  background-color: #ffdc78;
}

.alert-text {
  color: #e5faff;
  font-size: 14px;
  flex: 1;
}

.alert-text.small {
  font-size: 12px;
}

.red-text {
  color: #ff695a;
}

.orange-text {
  color: #ff8e3c;
}

.yellow-text {
  color: #ffdc78;
}

.alert-number {
  color: #e5faff;
  font-size: 20px;
  font-weight: bold;
  margin-left: auto;
}

.trend-chart {
  margin-right: 10px;
}
</style>
