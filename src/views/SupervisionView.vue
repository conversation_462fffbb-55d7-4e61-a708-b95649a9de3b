<template>
  <div class="supervision-container">
    <div class="supervision-content">
      <div class="nav-bar">
        <div
          v-for="item in navItems"
          :key="item.id"
          class="nav-item"
          :class="{ active: activeNavItem === item.id }"
          @click="handleNavClick(item.id)"
        >
          <img class="nav-icon" :src="item.icon" :alt="item.name" />
          <div class="nav-text">{{ item.name }}</div>
        </div>
      </div>
      <div class="supervision-grid">
        <div class="grid-item top-left">
          <TaskOverview />
        </div>
        <div class="grid-item top-right">
          <TaskSummary />
        </div>
        <div class="grid-item bottom-left">
          <MyTasks />
        </div>
        <div class="grid-item bottom-right">
          <CompletionStatus />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import jcxx from '@/assets/dashboard/backgrounds/jcxx.png'
import yjxx from '@/assets/dashboard/backgrounds/yjxx.png'
import djcx from '@/assets/dashboard/backgrounds/djcx.png'
import ddxx from '@/assets/dashboard/backgrounds/dddb.png'
import TaskOverview from '@/components/supervision/TaskOverview.vue'
import TaskSummary from '@/components/supervision/TaskSummary.vue'
import MyTasks from '@/components/supervision/MyTasks.vue'
import CompletionStatus from '@/components/supervision/CompletionStatus.vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const activeNavItem = ref('supervision')
const navItems = ref([
  { id: 'basic', name: '基础信息', icon: jcxx, url: '' },
  { id: 'warning', name: '预警信息', icon: yjxx, url: '' },
  { id: 'achievement', name: '打击成效', icon: djcx, url: '' },
  { id: 'supervision', name: '督导盯办', icon: ddxx, url: '' },
])

// 获取外部配置
const getExternalConfig = async () => {
  try {
    // 通过动态创建script标签加载外部配置
    return new Promise((resolve) => {
      if (window.externalConfig) {
        return resolve(window.externalConfig)
      }

      const script = document.createElement('script')
      script.src = '/config.js'
      script.onload = () => resolve(window.externalConfig || {})
      script.onerror = () => {
        console.warn('无法加载外部配置文件，使用默认配置')
        resolve({})
      }
      document.head.appendChild(script)
    })
  } catch (error) {
    console.error('加载配置失败:', error)
    return {}
  }
}

const handleNavClick = async (id) => {
  activeNavItem.value = id
  if (id === 'basic') {
    router.push('/')
  } else if (id === 'warning') {
    const externalConfig = await getExternalConfig()
    // 获取token
    const token = sessionStorage.getItem('access_token')

    // 构建带token的URL
    let url = externalConfig.navUrls[id]
    if (token && url) {
      const separator = url.includes('?') ? '&' : '?'
      url = `${url}${separator}access_token=${token}`
    }

    window.open(url, '_blank')
  } else if (id === 'achievement') {
    router.push({ name: 'strike-effect' })
  } else if (id === 'supervision') {
    // router.push({ name: 'supervision' })
  }
}
</script>

<style scoped>
.supervision-container {
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
  font-family: var(--font-family-base);
}
.supervision-content {
  position: relative;
  padding: 20px;
  height: calc(100% - 82px); /* 减去标题高度 */
  display: flex;
  flex-direction: column;
}
.nav-bar {
  display: flex;
  justify-content: space-around;
  width: 800px;
  height: 70px;
  margin: 0 auto;
  border-radius: 10px;
  z-index: 100;
  padding: 5px;
}
.nav-item {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  width: 160px;
  height: 58px;
  margin: 0 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #fff;
  padding: 0 15px;
  position: relative;
}
.nav-item:hover {
  background: rgba(0, 0, 0, 0.3);
}
.nav-item.active {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.nav-icon {
  width: 144px;
  height: 58px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
.nav-text {
  font-family: PangMenZhengDao;
  font-size: 20px;
  color: #e5faff;
  letter-spacing: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  font-weight: 400;
  padding-left: 48px;
}
.supervision-grid {
  display: grid;
  grid-template-columns: 618px 1230px;
  grid-template-rows: 423px 423px;
  gap: 20px;
  height: calc(100% - 90px); /* 减去导航栏高度和间距 */
  margin-top: 20px;
}
.grid-item {
  width: 100%;
  height: 100%;
}
</style>
