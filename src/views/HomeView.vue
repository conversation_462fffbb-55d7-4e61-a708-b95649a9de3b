<script setup>
// HomeView.vue - 大屏首页
// 导入资源
import { ref } from 'vue'
import LeftTopPanel from '@/components/dashboard/LeftTopPanel.vue'
import LeftBottomPanel from '@/components/dashboard/LeftBottomPanel.vue'
import RightTopPanel from '@/components/dashboard/RightTopPanel.vue'
import RightBottomPanel from '@/components/dashboard/RightBottomPanel.vue'
import BottomPanel1 from '@/components/dashboard/BottomPanel1.vue'
import BottomPanel3 from '@/components/dashboard/BottomPanel3.vue'
import BottomPanel4 from '@/components/dashboard/BottomPanel4.vue'
import AnalysisModal from '@/components/dashboard/AnalysisModal.vue'
</script>

<template>
  <div class="dashboard-container">
    <div class="dashboard-content">
      <!-- 左侧面板 -->
      <div class="left-panels">
        <LeftTopPanel />
        <LeftBottomPanel />
      </div>

      <!-- 中间区域（路由视图） -->
      <div class="center-area">
        <router-view></router-view>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panels">
        <RightTopPanel />
        <RightBottomPanel />
      </div>

      <!-- 底部面板 -->
      <div class="bottom-panels">
        <BottomPanel1 />
        <BottomPanel3 />
        <BottomPanel4 />
      </div>
    </div>
  </div>
</template>

<style scoped>
.dashboard-container {
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
  font-family: var(--font-family-base);
}

.main-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  object-fit: cover;
}

.dashboard-content {
  position: relative;
  padding: 20px;
  height: calc(100% - 82px); /* 减去标题高度 */
  display: grid;
  grid-template-columns: 420px 1fr 420px;
  grid-template-rows: auto auto;
}

.left-panels,
.right-panels {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.center-area {
  width: 1040px;
  height: calc(1080px - 308px - 136px);
}

.bottom-panels {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
  justify-content: space-between;
}
</style>
