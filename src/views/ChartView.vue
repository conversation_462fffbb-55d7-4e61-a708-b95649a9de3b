<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { use } from 'echarts/core'
import { useRoute } from 'vue-router'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart } from 'echarts/charts'
import dashboardApi from '@/api/dashboard'
import { useQueryConditionStore } from '@/stores/queryCondition'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  MarkLineComponent,
  MarkPointComponent,
} from 'echarts/components'
import VChart from 'vue-echarts'

// 注册必须的组件
use([
  CanvasRenderer,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  MarkLineComponent,
  MarkPointComponent,
])

//获取路由参数
const route = useRoute()
const type = route.query.type
const queryStore = useQueryConditionStore()

// 数据加载状态
const loading = ref(false)

const jin12 = ref([])
const qian12 = ref([])

// 创建标题映射对象
const titleMappings = {
  zsaj: {
    chart1: '走私案件-立案态势',
    chart2: '走私案件-抓获态势',
    chart3: '走私案件-刑拘态势',
    chart4: '走私案件-逮捕态势',
  },
  fhgbj: {
    chart1: '妨害国边境-刑拘态势',
    chart2: '妨害国边境-组织态势',
    chart3: '妨害国边境-运送态势',
    chart4: '妨害国边境-偷越态势',
  },
  bjsdsq: {
    // chart1: '涉枪抓获态势',
    // chart2: '缴获枪支态势',
    // chart3: '抓获人员态势',
    // chart4: '查处吸毒态势',
    chart1: '边境涉毒涉枪-刑事抓获人员态势',
    chart2: '边境涉毒涉枪-行政查处吸毒态势',
    chart3: '边境涉毒涉枪-涉枪抓获态势',
    chart4: '边境涉毒涉枪-缴获枪支态势',
  },
  kabfjc: {
    chart1: '口岸边防-人员出境态势',
    chart2: '口岸边防-人员入境态势',
    chart3: '口岸边防-车辆出境态势',
    chart4: '口岸边防-车辆入境态势',
  },
  bjjc: {
    chart1: '边境检查站缉查-车辆通行态势(进)',
    chart2: '边境检查站缉查-车辆通行态势(出)',
    chart3: '边境检查站缉查-人员通行态势(进)',
    chart4: '边境检查站缉查-人员通行态势(出)',
  },
  crjzj: {
    chart1: '出入境证件办理-中国人办证趋势',
    chart2: '出入境证件办理-外国人办证趋势',
    chart3: '出入境证件办理-不予签发人员趋势',
    chart4: '出入境证件办理-外国人临住登记趋势',
  },
  // 可以在此处添加更多类型的映射
}

// 数据键映射
const dataKeyMappings = {
  zsaj: {
    chart1: 'lasData', // 立案态势
    chart2: 'zhData', // 抓获态势
    chart3: 'xjData', // 刑拘态势
    chart4: 'dbData', // 逮捕态势
  },
  fhgbj: {
    chart1: 'xjrsData', // 刑拘态势
    chart2: 'zzData', // 组织态势
    chart3: 'ysData', // 运送态势
    chart4: 'tyData', // 偷越态势
  },
  bjsdsq: {
    chart1: 'zhrsData', // 抓获人员态势
    chart2: 'xdrsData', // 查处吸毒态势
    chart3: 'sqzhrsData', // 涉枪抓获态势
    chart4: 'jhqzsData', // 缴获枪支态势
  },
  kabfjc: {
    chart1: 'cjRyData', // 出境人员态势
    chart2: 'rjRyData', // 入境人员态势
    chart3: 'cjJtData', // 出境交通态势
    chart4: 'rjJtData', // 入境交通态势
  },
  bjjc: {
    chart1: 'clJrData', // 车辆进入态势
    chart2: 'clLkData', // 车辆离开态势
    chart3: 'ryJrData', // 人员进入态势
    chart4: 'ryLkData', // 人员离开态势
  },
  crjzj: {
    chart1: 'zgrbzs', // 中国人办证数
    chart2: 'wgrbzs', // 外国人办证数
    chart3: 'byqfrys', // 不予签发人员
    chart4: 'wgrlzdj', // 外国人临住登记
  },
}

const chartData = reactive([
  {
    id: 'chart1',
    name: titleMappings[type]?.chart1 || '刑事案件态势',
    data: {
      current: [], // 近12个月数据
      previous: [], // 前12个月数据
    },
  },
  {
    id: 'chart2',
    name: titleMappings[type]?.chart2 || '刑拘人数态势',
    data: {
      current: [], // 近12个月数据
      previous: [], // 前12个月数据
    },
  },
  {
    id: 'chart3',
    name: titleMappings[type]?.chart3 || '行政案件态势',
    data: {
      current: [], // 近12个月数据
      previous: [], // 前12个月数据
    },
  },
  {
    id: 'chart4',
    name: titleMappings[type]?.chart4 || '处罚人数态势',
    data: {
      current: [], // 近12个月数据
      previous: [], // 前12个月数据
    },
  },
])

// 监听路由参数变化
watch(
  () => route.query.type,
  (newType) => {
    // 更新图表数据
    const mappings = titleMappings[newType] || titleMappings.kabfjc
    chartData[0].name = mappings.chart1
    chartData[1].name = mappings.chart2
    chartData[2].name = mappings.chart3
    chartData[3].name = mappings.chart4

    // 加载新的数据
    loadChartData(newType)
  },
  { immediate: true },
)

// 加载图表数据
const loadChartData = async (chartType) => {
  loading.value = true
  try {
    // 获取当前部门
    const dept = queryStore.dept || '45'

    // 获取近12个月数据
    const currentData = await fetchTsData(chartType, '12', dept)

    // 获取前12个月数据
    const previousData = await fetchTsData(chartType, '24', dept)

    // 更新图表数据
    updateChartData(chartType, currentData, previousData)
  } catch (error) {
    console.error(`加载${chartType}态势数据失败:`, error)
  } finally {
    loading.value = false
  }
}

// 根据类型获取态势数据
const fetchTsData = async (chartType, tsTime, dept) => {
  switch (chartType) {
    case 'fhgbj':
      return await dashboardApi.getfhgbjTs(dept, tsTime)
    case 'zsaj':
      return await dashboardApi.getZsajqkTs(dept, tsTime)
    case 'bjsdsq':
      return await dashboardApi.getBjsdsqfzTs(dept, tsTime)
    case 'kabfjc':
      return await dashboardApi.getKabfjcqkTs(dept, tsTime)
    case 'bjjc':
      return await dashboardApi.getBjjczcjqkTs(dept, tsTime)
    case 'crjzj':
      return await dashboardApi.getCrjzjblqkTsAll(dept, tsTime)
    default:
      return null
  }
}

// 格式化图表数据
const formatChartData = (data) => {
  if (!data || !Array.isArray(data)) return []

  // 不再排序，直接提取数值
  return data.map((item) => item.TOTAL)
}

// 从数据中提取月份标签(仅显示月份，不显示年份)
const extractMonthLabels = (data) => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return Array(12)
      .fill(0)
      .map((_, i) => `${i + 1}月`)
  }

  // 提取月份，格式如 "3月"
  return data.map((item) => {
    const date = new Date(item.DAYTIME)
    return `${date.getMonth() + 1}月`
  })
}

// 更新图表数据
const updateChartData = (chartType, currentData, previousData) => {
  if (!currentData || !previousData) return

  const mapping = dataKeyMappings[chartType]
  if (!mapping) return

  // 存储X轴标签
  let xAxisLabels = []

  // 处理每个图表的数据
  chartData.forEach((chart, index) => {
    const chartIndex = `chart${index + 1}`
    const dataKey = mapping[chartIndex]

    if (dataKey && currentData[dataKey] && previousData[dataKey]) {
      // 处理近12个月数据
      chart.data.current = formatChartData(currentData[dataKey])

      // 处理前12个月数据
      chart.data.previous = formatChartData(previousData[dataKey])

      // 提取月份标签(仅对第一个图表处理一次)
      if (index === 0) {
        xAxisLabels = extractMonthLabels(currentData[dataKey])
        jin12.value = getYearData(currentData[dataKey])
        qian12.value = getYearData(previousData[dataKey])
      }
    }
  })
  console.log('近12个月', jin12.value)
  console.log('前12个月', qian12.value)

  // 将X轴标签保存到第一个图表中，其他图表会共用
  if (chartData[0] && xAxisLabels.length > 0) {
    chartData[0].monthLabels = xAxisLabels
  }
}

// 修改 getYearData 函数，返回一个月份到日期的映射对象
const getYearData = (data) => {
  const yearData = {}
  data.forEach((item) => {
    const date = new Date(item.DAYTIME)
    const month = `${date.getMonth() + 1}月`
    yearData[month] = item.DAYTIME
  })
  return yearData
}

// 获取图表配置
const getChartOption = (chart) => {
  // 设置Y轴最大值为数据中的最大值的1.2倍，最小为0
  const allValues = [...(chart.data.current || []), ...(chart.data.previous || [])]
  const maxValue = Math.max(...allValues.filter((v) => v !== undefined), 10) // 防止全部为0的情况
  const yAxisMax = Math.ceil(maxValue * 1.2)

  // 使用第一个图表的月份标签或默认值
  const monthLabels =
    chartData[0]?.monthLabels ||
    Array(12)
      .fill(0)
      .map((_, i) => `${i + 1}月`)

  return {
    title: {
      text: chart.name,
      left: '5%',
      top: '5%',
      textStyle: {
        color: '#FEDE4B',
        fontSize: 18,
        fontWeight: 'bold',
      },
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '15%',
      top: '25%',
      containLabel: true,
    },
    legend: {
      data: [
        {
          name: '近12个月',
          textStyle: {
            color: '#fff',
          },
          itemStyle: {
            color: '#FF0000',
          },
        },
        {
          name: '前12个月',
          textStyle: {
            color: '#fff',
          },
          itemStyle: {
            color: '#00FF00',
          },
        },
      ],
      icon: 'circle',
      itemWidth: 10,
      itemHeight: 10,
      right: '5%',
      top: '5%',
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        let result = params[0].axisValue + '<br/>'
        params.forEach((param) => {
          const color = param.seriesName === '近12个月' ? '#FF0000' : '#00FF00'
          const timeInfo =
            param.seriesName === '近12个月'
              ? jin12.value[param.axisValue] || param.axisValue
              : qian12.value[param.axisValue] || param.axisValue
          result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`
          result += `${param.seriesName} (${timeInfo}): ${param.value}<br/>`
        })
        return result
      },
      backgroundColor: 'rgba(0,24,41,0.8)',
      borderColor: '#5ac8fa',
      textStyle: {
        color: '#fff',
      },
    },
    xAxis: {
      type: 'category',
      data: monthLabels,
      axisLine: {
        show: true,
        lineStyle: {
          color: '#fff',
          opacity: 0.2,
        },
      },
      axisLabel: {
        color: '#fff',
        show: true,
        interval: 0, // 强制显示所有标签
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: yAxisMax,
      interval: Math.ceil(yAxisMax / 4),
      axisLine: {
        show: false,
      },
      axisLabel: {
        color: '#fff',
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
    },
    series: [
      {
        name: '近12个月',
        type: 'line',
        data: chart.data.current.length > 0 ? chart.data.current : Array(12).fill(0),
        smooth: true,
        showSymbol: false,
        lineStyle: {
          width: 3,
          color: '#FF0000',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(255, 0, 0, 0.5)',
              },
              {
                offset: 1,
                color: 'rgba(255, 0, 0, 0.01)',
              },
            ],
          },
        },
      },
      {
        name: '前12个月',
        type: 'line',
        data: chart.data.previous.length > 0 ? chart.data.previous : Array(12).fill(0),
        smooth: true,
        showSymbol: false,
        lineStyle: {
          width: 3,
          color: '#00FF00',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(0, 255, 0, 0.5)',
              },
              {
                offset: 1,
                color: 'rgba(0, 255, 0, 0.01)',
              },
            ],
          },
        },
      },
    ],
  }
}

// 自动调整图表
const autoresize = ref(true)

// 组件挂载时加载数据
onMounted(() => {
  loadChartData(type)
})

// 监听查询条件变化
queryStore.$subscribe(() => {
  loadChartData(type)
})
</script>

<template>
  <div class="chart-container">
    <div class="chart-content">
      <img src="@/assets/dashboard/backgrounds/trend-bg.png" alt="chart-bg" class="chart-bg" />

      <!-- 加载指示器 -->
      <div v-if="loading" class="loading-overlay">
        <el-icon class="loading-icon"><loading /></el-icon>
        <span>加载中...</span>
      </div>

      <div class="charts-grid">
        <div v-for="chart in chartData" :key="chart.id" class="chart-item">
          <v-chart :option="getChartOption(chart)" :autoresize="autoresize" class="chart" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
}
.chart-content {
  position: relative;
  width: 100%;
  height: 100%;
}
.chart-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  width: 100%;
  height: 100%;
}

.chart-item {
  width: 100%;
  height: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #e5faff;
  z-index: 10;
}

.loading-icon {
  font-size: 24px;
  margin-bottom: 8px;
  animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
