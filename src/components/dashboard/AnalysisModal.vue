<script setup>
import { ref, reactive, onMounted, watch, nextTick, computed } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  MarkLineComponent,
  MarkPointComponent,
} from 'echarts/components'
import VChart from 'vue-echarts'
import titlebg from '@/assets/modal/titlebg.png'
import titleIcon from '@/assets/modal/tile_icon.png'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import * as analysisModalApi from '@/api/analysisModal'

// 注册必须的组件
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  MarkLineComponent,
  MarkPointComponent,
])

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue'])

const handleClose = () => {
  emit('update:modelValue', false)
}

// 阻止点击内容区域时模态框关闭
const handleContentClick = (event) => {
  event.stopPropagation()
}

// 数据加载状态
const loading = ref(false)

// 详情模态框相关状态
const detailModalVisible = ref(false)
const detailLoading = ref(false)
const detailData = ref([])
const detailTotal = ref(0)
const detailCurrentPage = ref(1)
const detailPageSize = ref(10)
const detailType = ref('')

// 详情模态框标题映射
const detailTitleMap = {
  CCRS: '打击处理数详情',
  ZZYS: '刑事组织运送数详情',
  TY: '刑事偷越总数详情',
}

// 详情模态框参数映射
const detailTypeMap = {
  打击处理数: 'CCRS',
  刑事组织运送数: 'ZZYS',
  刑事偷越总数: 'TY',
}

// 选择的模块
const selectedModule = ref('fhgbj')
const moduleOptions = [
  { value: 'fhgbj', label: '妨害国边境违法犯罪' },
  { value: 'zsaj', label: '走私案件' },
  { value: 'bjsdsq', label: '边境涉毒涉枪' },
  { value: 'txfx', label: '通信信号分析' },
  // { value: 'kabfjc', label: '口岸边防检查' },
  // { value: 'bjjc', label: '边境检查站缉查' },
  // { value: 'crjzj', label: '出入境证件办理' },
]

// 添加单位选择
const selecteDept = ref('')

// 基础城市选项
const baseCityOptions = [
  { value: '450100000000', label: '南宁市' },
  { value: '450200000000', label: '柳州市' },
  { value: '450300000000', label: '桂林市' },
  { value: '450400000000', label: '梧州市' },
  { value: '450500000000', label: '北海市' },
  { value: '450600000000', label: '防城港市' },
  { value: '450700000000', label: '钦州市' },
  { value: '450800000000', label: '贵港市' },
  { value: '450900000000', label: '玉林市' },
  { value: '451000000000', label: '百色市' },
  { value: '451100000000', label: '贺州市' },
  { value: '451200000000', label: '河池市' },
  { value: '451300000000', label: '来宾市' },
  { value: '451400000000', label: '崇左市' },
  { value: '458517000000', label: '百色边境管理支队' },
  { value: '458518000000', label: '崇左边境管理支队' },
  { value: '458519000000', label: '防城港边境管理支队' },
  { value: '451026000000', label: '那坡县' },
  { value: '451081000000', label: '靖西市' },
  { value: '450681000000', label: '东兴市' },
  { value: '450603000000', label: '防城区' },
  { value: '451422000000', label: '宁明县' },
  { value: '451423000000', label: '龙州县' },
  { value: '451424000000', label: '大新县' },
  { value: '451481000000', label: '凭祥市' },
]

// 妨害国边境违法犯罪模块的额外选项
const fhgbjExtraOptions = [
  { value: '451000000000_GA', label: '百色公安451000000000' },
  { value: '451400000000_GA', label: '崇左公安451400000000' },
  { value: '450600000000_GA', label: '防城港公安450600000000' },
]

// 动态计算城市选项
const cityOptions = computed(() => {
  if (selectedModule.value === 'fhgbj') {
    // 妨害国边境违法犯罪模块时，添加额外选项
    return [...baseCityOptions, ...fhgbjExtraOptions]
  }
  return baseCityOptions
})

// 分县局选择
const selectedSubDept = ref('')
const subDeptOptions = ref([])
const subDeptLoading = ref(false)

// 日期范围
const dateRange = ref([new Date(new Date().getFullYear(), 0, 1), new Date()])
const selectedShortcut = ref('') // 添加存储当前选中快捷选项的变量

// 日期开始时间变化处理
const handleStartDateChange = (value) => {
  if (value && typeof value === 'object' && value.length === 2) {
    // 这是从快捷选项中选择的日期范围
    dateRange.value = [value[0], value[1]]
    console.log('通过快捷选项更新日期范围', dateRange.value)
  } else if (value && dateRange.value[1]) {
    dateRange.value = [value, dateRange.value[1]]
    console.log('开始日期已更新', dateRange.value)
  } else if (value) {
    dateRange.value = [value, null]
  }
}

// 日期结束时间变化处理
const handleEndDateChange = (value) => {
  if (value && typeof value === 'object' && value.length === 2) {
    // 这是从快捷选项中选择的日期范围
    dateRange.value = [value[0], value[1]]
    console.log('通过快捷选项更新日期范围', dateRange.value)
  } else if (value && dateRange.value[0]) {
    dateRange.value = [dateRange.value[0], value]
    console.log('结束日期已更新', dateRange.value)
  } else if (value) {
    dateRange.value = [null, value]
  }
}

// 日期选择器的快捷选项
const shortcuts = [
  {
    text: '专项行动以来',
    value: () => {
      const now = dayjs()
      const start = dayjs('2024-08-22')
      const end = now
      return [start.toDate(), end.toDate()]
    },
  },
  {
    text: '本年',
    value: () => {
      const now = dayjs()
      const start = dayjs().startOf('year')
      return [start.toDate(), now.toDate()]
    },
  },
  {
    text: '本季',
    value: () => {
      const now = dayjs()
      const quarter = Math.floor(now.month() / 3)
      const start = dayjs()
        .year(now.year())
        .month(quarter * 3)
        .startOf('month')
      return [start.toDate(), now.toDate()]
    },
  },
  {
    text: '本月',
    value: () => {
      const now = dayjs()
      const start = dayjs().startOf('month')
      return [start.toDate(), now.toDate()]
    },
  },
]

// 处理快捷选择
const handleShortcutChange = (index) => {
  if (index !== null && index !== undefined && shortcuts[index]) {
    const dateRange1 = shortcuts[index].value()
    dateRange.value = [dateRange1[0], dateRange1[1]]
  }
}

// 图表数据
const chartData = reactive({
  xAxis: [],
  series: [],
  rawData: null, // 存储原始数据，用于切换指标时重新处理
  originalDates: [], // 存储对应X轴的原始日期值
  currentPeriodDates: [], // 本期原始日期
  lastPeriodDates: [], // 同期原始日期
})

// 概览数据
const summaryData = reactive({
  currentYear: {
    title: '',
    data: [],
  },
  lastYear: {
    title: '',
    data: [],
  },
  beforeLastYear: {
    title: '',
    data: [],
  },
})

// 图表类型
const chartType = ref('line') // 'line', 'bar' 或 'compareBar'
const chartTabs = [
  { key: 'line', label: '折线图' },
  { key: 'bar', label: '柱状图' },
  { key: 'compareBar', label: '同比柱状图' },
]

// legend默认全未选中
const legendSelectedKeys = ref([])

// 监听chartType变化
watch(chartType, () => {
  console.log('chartType变化', chartType.value)
  // 不需要增加key，chartOption计算属性会自动响应chartType变化
  // nextTick(() => {
  //   // 增加key以重新渲染图表并触发动画
  //   chartKey.value += 1
  // })
})

// 监听legend事件
const onLegendSelectChanged = (params) => {
  console.log('legend选择变化', params)
  // 获取选中的图例项
  const selected = params.selected || {}
  // 更新选中的图例键
  const newSelectedKeys = Object.keys(selected).filter((key) => selected[key])

  // 限制最多选中5个图例
  if (newSelectedKeys.length > 5) {
    // 如果是取消选中，则直接更新
    if (newSelectedKeys.length < legendSelectedKeys.value.length) {
      legendSelectedKeys.value = newSelectedKeys
    } else {
      // 如果是新增选中，且总数超过5个，则提示用户
      console.log('最多只能选择5个图例')
      // 弹出提示信息
      ElMessage.warning('最多只能选择5个图例项')
      // 保持原有选中状态不变
      return
    }
  } else {
    // 否则正常更新选中状态
    legendSelectedKeys.value = newSelectedKeys
  }

  console.log('更新后的legendSelectedKeys', legendSelectedKeys.value)
}

// 只在柱状图模式下使用的legend.selected对象
const legendSelectedObj = computed(() => {
  const obj = {}
  chartData.series.forEach((s) => {
    obj[s.name] = legendSelectedKeys.value.includes(s.name)
  })
  return obj
})

// 只展示legend选中的数据
const filteredSummaryData = computed(() => {
  // 如果是折线图或同比柱状图模式，只显示当前选中指标的数据
  if ((chartType.value === 'line' || chartType.value === 'compareBar') && selectedIndicator.value) {
    // 获取选中指标的配置
    const indicatorConfig = analysisModalApi.moduleConfig[
      selectedModule.value
    ]?.trendIndicators.find((item) => item.key === selectedIndicator.value)

    if (!indicatorConfig)
      return { currentYear: { data: [] }, lastYear: { data: [] }, beforeLastYear: { data: [] } }

    // 根据指标名称筛选
    const indicatorName = indicatorConfig.name
    console.log('折线图/同比柱状图模式 - 选中指标名称:', indicatorName)
    console.log(
      '概览数据标签:',
      summaryData.currentYear.data.map((item) => item.label),
    )

    // 筛选函数
    const filterSingleIndicator = (year) => {
      // 只查找精确匹配的数据项
      const matchedItem = year.data.find((item) => item.label === indicatorName)

      if (matchedItem) {
        return {
          ...year,
          data: [matchedItem],
        }
      }

      // 如果没找到精确匹配，返回空数据
      return {
        ...year,
        data: [],
      }
    }

    return {
      currentYear: filterSingleIndicator(summaryData.currentYear),
      lastYear: filterSingleIndicator(summaryData.lastYear),
      beforeLastYear: filterSingleIndicator(summaryData.beforeLastYear),
    }
  }

  // 柱状图模式下，才根据选中的图例过滤数据
  if (!legendSelectedKeys.value.length)
    return { currentYear: { data: [] }, lastYear: { data: [] }, beforeLastYear: { data: [] } }

  // 用legend选中的name和summaryData.data.label匹配
  const filterYear = (year) => {
    // 记录日志
    console.log('柱状图模式 - 选中图例:', legendSelectedKeys.value)
    console.log(
      '概览数据标签:',
      year.data.map((item) => item.label),
    )

    // 使用精确匹配
    const exactMatches = legendSelectedKeys.value
      .map((name) => year.data.find((d) => d.label === name))
      .filter(Boolean)

    if (exactMatches.length > 0) {
      return {
        ...year,
        data: exactMatches,
      }
    }

    // 如果精确匹配没有结果，尝试使用模糊匹配
    const fuzzyMatches = []
    for (const name of legendSelectedKeys.value) {
      for (const item of year.data) {
        // 检查是否有部分匹配
        if (name.includes(item.label) || item.label.includes(name)) {
          fuzzyMatches.push(item)
        }
      }
    }

    return {
      ...year,
      data: fuzzyMatches,
    }
  }

  return {
    currentYear: filterYear(summaryData.currentYear),
    lastYear: filterYear(summaryData.lastYear),
    beforeLastYear: filterYear(summaryData.beforeLastYear),
  }
})

// 自动调整图表
const autoresize = ref(true)

// 选择的指标（用于折线图模式的单选）
const selectedIndicator = ref('')

// 为折线图模式生成指标选项
const indicatorOptions = computed(() => {
  if (!selectedModule.value || !analysisModalApi.moduleConfig[selectedModule.value]) return []

  const config = analysisModalApi.moduleConfig[selectedModule.value]
  return config.trendIndicators.map((indicator) => ({
    value: indicator.key,
    label: indicator.name,
    color: indicator.color,
  }))
})

// 监听图表类型变化，重置图例选择状态
watch(chartType, (newType, oldType) => {
  console.log('chartType变化', newType, '原类型:', oldType)

  if (newType === 'line' || newType === 'compareBar') {
    // 折线图和同比柱状图模式时，如果还没有选中指标，则默认选择第一个指标
    if (!selectedIndicator.value && indicatorOptions.value.length > 0) {
      selectedIndicator.value = indicatorOptions.value[0].value
    }
    legendSelectedKeys.value = ['本期', '同期']
    // 如果有原始数据，重新处理数据
    if (chartData.rawData) {
      if (newType === 'line') {
        processLineChartData()
      } else {
        // 同比柱状图现在也使用折线图的数据处理方式
        processCompareBarChartData()
      }
    }
  } else {
    // 柱状图模式时，重新获取数据以确保正确显示
    if (chartData.rawData) {
      // 从其他图表切换到柱状图时，确保重新生成x轴数据
      const chartResult = analysisModalApi.processChartData(selectedModule.value, chartData.rawData)
      chartData.xAxis = chartResult.xAxis
      chartData.series = chartResult.series
      // 重置图例选择状态为空
      legendSelectedKeys.value = []
    }
  }
})

// 监听模块变化，重置指标选择
watch(selectedModule, () => {
  // 重置指标选择
  if (indicatorOptions.value.length > 0) {
    selectedIndicator.value = indicatorOptions.value[0].value
  }

  // 这里需要根据当前的图表类型来决定如何处理数据
  handleSearch().then(() => {
    // 搜索完成后，如果是同比柱状图模式，强制重新处理数据为同比柱状图格式
    if (chartType.value === 'compareBar') {
      nextTick(() => {
        processCompareBarChartData()
      })
    }
  })
})

// 监听指标变化，更新图表数据
watch(selectedIndicator, () => {
  if (chartType.value === 'line') {
    processLineChartData()
  } else if (chartType.value === 'compareBar') {
    processCompareBarChartData()
  }
})

// 处理折线图数据的特殊方法
const processLineChartData = (fromCompareBar = false) => {
  if (!selectedIndicator.value || !chartData.rawData) return

  const rawData = chartData.rawData
  const indicatorKey = selectedIndicator.value

  // 获取选中指标的配置
  const indicatorConfig = analysisModalApi.moduleConfig[selectedModule.value]?.trendIndicators.find(
    (item) => item.key === indicatorKey,
  )

  if (!indicatorConfig) return

  // 正确的同期数据键名格式：基础名称+Qn+Data
  // 例如：xjrsData -> xjrsQnData
  const baseKey = indicatorKey.replace('Data', '')
  const qnIndicatorKey = `${baseKey}QnData`

  console.log('当前指标键:', indicatorKey)
  console.log('同期指标键:', qnIndicatorKey)
  console.log('原始数据键列表:', Object.keys(rawData))

  // 如果是从同比柱状图切换过来或强制更新，则始终重新设置x轴标签
  if (fromCompareBar || !chartData.xAxis.includes('月')) {
    if (rawData[indicatorKey] && Array.isArray(rawData[indicatorKey])) {
      // 保存原始日期数据
      chartData.originalDates = rawData[indicatorKey].map((item) => item.DAYTIME)
      // 保存本期日期
      chartData.currentPeriodDates = rawData[indicatorKey].map((item) => item.DAYTIME)

      // 保存同期日期（通常是上一年对应月份）
      if (rawData[qnIndicatorKey] && Array.isArray(rawData[qnIndicatorKey])) {
        chartData.lastPeriodDates = rawData[qnIndicatorKey].map((item) => item.DAYTIME)
      }

      chartData.xAxis = rawData[indicatorKey].map((item) => {
        const month = new Date(item.DAYTIME).getMonth() + 1
        return `${month}月`
      })
      console.log('重新设置x轴为月份标签:', chartData.xAxis)
      console.log('保存本期原始日期:', chartData.currentPeriodDates)
      console.log('保存同期原始日期:', chartData.lastPeriodDates)
    }
  }

  console.log('rawData', rawData)
  console.log('indicatorKey', indicatorKey)
  console.log('qnIndicatorKey', qnIndicatorKey)
  console.log('rawData[indicatorKey]', rawData[indicatorKey])
  console.log('rawData[qnIndicatorKey]', rawData[qnIndicatorKey])
  // 本期数据
  const currentPeriodData = rawData[indicatorKey]?.map((item) => item.TOTAL || 0) || []

  // 同期数据
  const lastYearData = rawData[qnIndicatorKey]?.map((item) => item.TOTAL || 0) || []

  // 设置图表数据，确保两个系列使用不同的颜色
  chartData.series = [
    {
      name: '本期',
      data: currentPeriodData,
      color: '#FF4B4B', // 明显的红色
    },
    {
      name: '同期',
      data: lastYearData,
      color: '#00FF00', // 明显的绿色
    },
  ]

  // 设置图例为本期和同期
  legendSelectedKeys.value = ['本期', '同期']
}

// 处理同比柱状图数据的特殊方法 - 现在使用与折线图相同的数据
const processCompareBarChartData = () => {
  if (!selectedIndicator.value || !chartData.rawData) return

  // 直接调用折线图数据处理方法，复用代码
  processLineChartData()

  // 只修改series的类型为bar
  chartData.series = chartData.series.map((item) => ({
    ...item,
    type: 'bar',
    barWidth: 10, // 柱子宽度设置小一些，因为有多个月份的数据
  }))
}

// 创建两个不同的图表选项计算属性
const lineChartOption = computed(() => {
  // 避免传入空数据导致错误
  if (chartData.series.length === 0) {
    return {
      grid: {
        left: '3%',
        right: '15%',
        bottom: '3%',
        top: '15%',
        containLabel: true,
      },
      xAxis: { type: 'category', data: [] },
      yAxis: { type: 'value' },
      series: [],
    }
  }

  // 创建一个颜色映射，确保本期和同期使用不同颜色
  const colorMap = {}
  chartData.series.forEach((series) => {
    colorMap[series.name] = series.color
  })

  // 折线图特有配置
  return {
    grid: {
      left: '3%',
      right: '15%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    legend: {
      data: ['本期', '同期'].map((name) => {
        return {
          name,
          itemStyle: {
            color: colorMap[name] || (name === '本期' ? '#FF4B4B' : '#00FF00'),
          },
        }
      }),
      icon: 'circle',
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        color: '#ffffff',
        fontSize: 16,
        fontWeight: 'bold',
      },
      right: '5%',
      top: '0%',
      selected: {
        本期: true,
        同期: true,
      },
      selectedMode: 'multiple',
      itemGap: 30,
      backgroundColor: 'rgba(0, 24, 48, 0.5)',
      borderColor: 'rgba(0, 208, 255, 0.2)',
      borderWidth: 1,
      borderRadius: 4,
      padding: [8, 20],
      width: '90%',
      align: 'auto',
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,24,41,0.8)',
      borderColor: '#5ac8fa',
      textStyle: {
        color: '#fff',
      },
      formatter: function (params) {
        let result = ''
        params.forEach((item) => {
          // 根据系列名称使用不同的日期数据
          let displayDate = ''
          if (item.seriesName === '本期') {
            // 使用本期日期
            displayDate = chartData.currentPeriodDates[item.dataIndex]
              ? dayjs(chartData.currentPeriodDates[item.dataIndex]).format('YYYY-MM')
              : item.axisValue
          } else if (item.seriesName === '同期') {
            // 使用同期日期
            displayDate = chartData.lastPeriodDates[item.dataIndex]
              ? dayjs(chartData.lastPeriodDates[item.dataIndex]).format('YYYY-MM')
              : item.axisValue
          } else {
            // 其他系列使用默认日期
            displayDate = chartData.originalDates[item.dataIndex]
              ? dayjs(chartData.originalDates[item.dataIndex]).format('YYYY-MM')
              : item.axisValue
          }

          result += `${item.seriesName} (${displayDate}): ${item.value}<br/>`
        })
        return result
      },
    },
    color: Object.values(colorMap), // 直接设置全局颜色
    xAxis: {
      type: 'category',
      data: chartData.xAxis,
      axisLine: {
        show: true,
        lineStyle: {
          color: '#fff',
          opacity: 0.2,
        },
      },
      axisLabel: {
        color: '#fff',
        show: true,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisLabel: {
        color: '#fff',
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
    },
    series: chartData.series.map((item) => {
      // 计算数据长度，用于判断最后一个点
      const dataLength = item.data.length

      return {
        name: item.name,
        type: 'line',
        data: item.data,
        smooth: true,
        showSymbol: true,
        lineStyle: {
          width: 3,
          color: item.color, // 使用系列中设定的颜色
        },
        itemStyle: {
          color: item.color, // 使用系列中设定的颜色
        },
        animation: true,
        animationDuration: 2000,
        animationEasing: 'cubicInOut',
        // 使用markPoint替代label来显示最后一个点的信息
        markPoint: {
          symbol: 'circle',
          symbolSize: function (value, params) {
            // 只在最后一个点显示标记
            if (params.dataIndex === dataLength - 1) {
              return 10
            }
            return 0
          },
          label: {
            show: true,
            position: function (params) {
              // 本期标签在上方，同期标签在下方，避免重叠
              if (params.seriesName === '本期' || item.name === '本期') {
                return 'top'
              } else {
                return 'bottom'
              }
            },
            distance: 15,
            color: item.name === '本期' ? 'rgba(255, 0, 0, 1)' : 'rgba(0, 255, 0, 1)',
            fontSize: 16,
            fontWeight: 'bold',
            // backgroundColor:
            //   item.name === '本期' ? 'rgba(255, 75, 75, 0.8)' : 'rgba(0, 255, 0, 0.8)',
            padding: [6, 10],
            borderRadius: 4,
            formatter: function (params) {
              // 获取当前选中的指标的配置
              const indicatorConfig = analysisModalApi.moduleConfig[
                selectedModule.value
              ]?.trendIndicators.find((ind) => ind.key === selectedIndicator.value)

              if (!indicatorConfig) return params.value

              const indicatorName = indicatorConfig.name

              if (params.seriesName === '本期' || item.name === '本期') {
                const overviewItem = filteredSummaryData.value.currentYear.data.find(
                  (item) =>
                    item.label === indicatorName ||
                    indicatorName.includes(item.label) ||
                    item.label.includes(indicatorName),
                )

                if (overviewItem) {
                  const trendSymbol = overviewItem.same.startsWith('+') ? '上升' : '下降'
                  // 单行显示但简化内容
                  return `本期: ${overviewItem.value} 同比${trendSymbol}${overviewItem.same.replace(/[+-]/, '')}`
                }
                return `本期: ${params.value}`
              } else {
                const overviewItem = filteredSummaryData.value.lastYear.data.find(
                  (item) =>
                    item.label === indicatorName ||
                    indicatorName.includes(item.label) ||
                    item.label.includes(indicatorName),
                )

                if (overviewItem) {
                  // 单行显示
                  return `同期: ${overviewItem.value}`
                }
                return `同期: ${params.value}`
              }
            },
          },
          itemStyle: {
            color: item.color,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
            shadowBlur: 10,
          },
          data: [
            {
              type: 'max',
              name: '最后点',
              coord: [dataLength - 1, item.data[dataLength - 1]],
            },
          ],
        },
        // 普通数据点的标签
        label: {
          show: function (params) {
            // 普通点只显示数值
            return params.dataIndex !== dataLength - 1
          },
          position: 'top',
          color: '#fff',
          fontSize: 14,
          fontWeight: 'bold',
          formatter: '{c}',
        },
      }
    }),
  }
})

const barChartOption = computed(() => {
  // 避免传入空数据导致错误
  if (!chartData.series || chartData.series.length === 0) {
    return {
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '3%',
        containLabel: true,
      },
      xAxis: { type: 'category', data: [] },
      yAxis: { type: 'value' },
      series: [],
    }
  }

  // 柱状图特有配置
  // 特殊处理"妨害国边境违法犯罪"(fhgbj)模块的图例，分成三行显示
  let legendOption = {}

  if (selectedModule.value === 'fhgbj') {
    // 输出所有图表系列名称，用于调试
    console.log(
      '所有图表系列名称:',
      chartData.series.map((s) => s.name),
    )

    // 固定的三行图例顺序
    const row1Names = ['立案总数', '打击处理数', '刑事组织运送数', '刑事偷越总数']
    const row2Names = ['刑事立案数', '刑事刑拘人数', '刑事组织人数', '刑事偷越(中国人)']
    const row3Names = ['行政立案数', '行政处罚人数', '刑事运送人数', '刑事偷越(外国人)']

    // 筛选实际存在的图例
    const existingNames = chartData.series.map((s) => s.name)

    // 计算每个组存在的图例
    const group1 = row1Names.filter((name) => existingNames.includes(name))
    const group2 = row2Names.filter((name) => existingNames.includes(name))
    const group3 = row3Names.filter((name) => existingNames.includes(name))

    // 创建多个图例组件
    legendOption = {
      legend: [
        {
          // 第一行图例
          data: group1.map((name) => {
            const series = chartData.series.find((s) => s.name === name)
            return {
              name,
              itemStyle: {
                color: series ? series.color : '#ccc',
              },
            }
          }),
          top: '0%',
          left: 'center',
          textStyle: {
            color: '#ffffff',
            fontSize: 16,
            fontWeight: 'bold',
            width: 140,
          },
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 30,
          backgroundColor: 'rgba(0, 24, 48, 0.5)',
          borderColor: 'rgba(0, 208, 255, 0.2)',
          borderWidth: 1,
          borderRadius: 4,
          padding: [5, 20],
          icon: 'circle',
          selectedMode: 'multiple',
          selected: legendSelectedObj.value,
          width: '90%',
          align: 'auto',
          itemStyle: {
            borderWidth: 0,
          },
          formatter: function (name) {
            return name.padEnd(8, '\u3000')
          },
        },
        {
          // 第二行图例
          data: group2.map((name) => {
            const series = chartData.series.find((s) => s.name === name)
            return {
              name,
              itemStyle: {
                color: series ? series.color : '#ccc',
              },
            }
          }),
          top: '6%',
          left: 'center',
          textStyle: {
            color: '#ffffff',
            fontSize: 16,
            fontWeight: 'bold',
            width: 140,
          },
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 30,
          backgroundColor: 'rgba(0, 24, 48, 0.5)',
          borderColor: 'rgba(0, 208, 255, 0.2)',
          borderWidth: 1,
          borderRadius: 4,
          padding: [5, 20],
          icon: 'circle',
          selectedMode: 'multiple',
          selected: legendSelectedObj.value,
          width: '90%',
          align: 'auto',
          itemStyle: {
            borderWidth: 0,
          },
          formatter: function (name) {
            return name.padEnd(8, '\u3000')
          },
        },
        {
          // 第三行图例
          data: group3.map((name) => {
            const series = chartData.series.find((s) => s.name === name)
            return {
              name,
              itemStyle: {
                color: series ? series.color : '#ccc',
              },
            }
          }),
          top: '12%',
          left: 'center',
          textStyle: {
            color: '#ffffff',
            fontSize: 16,
            fontWeight: 'bold',
            width: 140,
          },
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 30,
          backgroundColor: 'rgba(0, 24, 48, 0.5)',
          borderColor: 'rgba(0, 208, 255, 0.2)',
          borderWidth: 1,
          borderRadius: 4,
          padding: [5, 20],
          icon: 'circle',
          selectedMode: 'multiple',
          selected: legendSelectedObj.value,
          width: '90%',
          align: 'auto',
          itemStyle: {
            borderWidth: 0,
          },
          formatter: function (name) {
            return name.padEnd(8, '\u3000')
          },
        },
      ],
    }
  } else {
    // 其他模块使用原来的单行图例
    legendOption = {
      legend: {
        data: chartData.series.map((item) => ({
          name: item.name,
          itemStyle: {
            color: item.color,
          },
        })),
        icon: 'circle',
        itemWidth: 12,
        itemHeight: 12,
        textStyle: {
          color: '#ffffff',
          fontSize: 16,
          fontWeight: 'bold',
        },
        right: '5%',
        top: '0%',
        selected: legendSelectedObj.value,
        selectedMode: 'multiple',
        itemGap: 30,
        backgroundColor: 'rgba(0, 24, 48, 0.5)',
        borderColor: 'rgba(0, 208, 255, 0.2)',
        borderWidth: 1,
        borderRadius: 4,
        padding: [8, 20],
        width: '90%',
        align: 'auto',
        formatter: function (name) {
          return name.padEnd(8, '\u3000')
        },
      },
    }
  }

  // 整合图表配置
  return {
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: selectedModule.value === 'fhgbj' ? '20%' : '15%',
      containLabel: true,
    },
    animation: true,
    animationDuration: 2000,
    animationEasing: 'cubicInOut',
    animationThreshold: 5000,
    animationDurationUpdate: 1800,
    animationEasingUpdate: 'cubicInOut',
    animationDelay: function (idx) {
      return idx * 200
    },
    animationDelayUpdate: function (idx) {
      return idx * 200
    },
    ...legendOption,
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,24,41,0.8)',
      borderColor: '#5ac8fa',
      textStyle: {
        color: '#fff',
      },
      // formatter: function (params) {
      //   let result = ''
      //   params.forEach((item) => {
      //     // 使用原始日期数据，如果有的话
      //     const originalDate = chartData.originalDates[item.dataIndex]
      //       ? dayjs(chartData.originalDates[item.dataIndex]).format('YYYY-MM-DD')
      //       : item.axisValue

      //     result += `${item.seriesName} (${originalDate}): ${item.value}<br/>`
      //   })
      //   return result
      // },
    },
    xAxis: {
      type: 'category',
      data: chartData.xAxis,
      axisLine: {
        show: true,
        lineStyle: {
          color: '#fff',
          opacity: 0.2,
        },
      },
      axisLabel: {
        color: '#fff',
        show: true,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisLabel: {
        color: '#fff',
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
    },
    series: chartData.series.map((item) => {
      const isSelected = legendSelectedObj.value[item.name]
      return {
        name: item.name,
        type: 'bar',
        data: item.data,
        barWidth: 18,
        animationOrigin: 'bottom',
        itemStyle: {
          color: item.color,
        },
        animation: true,
        animationDuration: 2000,
        animationEasing: 'cubicInOut',
        animationDelay: function (idx) {
          return idx * 200
        },
        label: {
          show: isSelected,
          position: 'top',
          color: '#fff',
          fontSize: 14,
          fontWeight: 'bold',
          formatter: (params) => params.value,
        },
      }
    }),
  }
})

// 创建一个同比柱状图的选项计算属性
const compareBarChartOption = computed(() => {
  // 避免传入空数据导致错误
  if (chartData.series.length === 0) {
    return {
      grid: {
        left: '3%',
        right: '15%',
        bottom: '3%',
        top: '15%',
        containLabel: true,
      },
      xAxis: { type: 'category', data: [] },
      yAxis: { type: 'value' },
      series: [],
    }
  }

  // 创建一个颜色映射，确保本期和同期使用不同颜色
  const colorMap = {}
  chartData.series.forEach((series) => {
    colorMap[series.name] = series.color
  })

  // 同比柱状图特有配置，但现在基于月份数据
  return {
    grid: {
      left: '3%',
      right: '15%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    legend: {
      data: ['本期', '同期'].map((name) => {
        return {
          name,
          itemStyle: {
            color: colorMap[name] || (name === '本期' ? '#FF4B4B' : '#00FF00'),
          },
        }
      }),
      icon: 'circle',
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        color: '#ffffff',
        fontSize: 16,
        fontWeight: 'bold',
      },
      right: '5%',
      top: '0%',
      selected: {
        本期: true,
        同期: true,
      },
      selectedMode: 'multiple',
      itemGap: 30,
      backgroundColor: 'rgba(0, 24, 48, 0.5)',
      borderColor: 'rgba(0, 208, 255, 0.2)',
      borderWidth: 1,
      borderRadius: 4,
      padding: [8, 20],
      width: '90%',
      align: 'auto',
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,24,41,0.8)',
      borderColor: '#5ac8fa',
      textStyle: {
        color: '#fff',
      },
      // formatter: function (params) {
      //   let result = ''
      //   params.forEach((item) => {
      //     // 使用原始日期数据，如果有的话
      //     const originalDate = chartData.originalDates[item.dataIndex]
      //       ? dayjs(chartData.originalDates[item.dataIndex]).format('YYYY-MM-DD')
      //       : item.axisValue

      //     result += `${item.seriesName} (${originalDate}): ${item.value}<br/>`
      //   })
      //   return result
      // },
    },
    color: Object.values(colorMap),
    xAxis: {
      type: 'category',
      data: chartData.xAxis,
      axisLine: {
        show: true,
        lineStyle: {
          color: '#fff',
          opacity: 0.2,
        },
      },
      axisLabel: {
        color: '#fff',
        show: true,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisLabel: {
        color: '#fff',
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
    },
    series: chartData.series.map((item) => ({
      name: item.name,
      type: 'bar',
      data: item.data,
      barWidth: 24,
      itemStyle: {
        color: item.color,
        borderRadius: [2, 2, 0, 0],
      },
      label: {
        show: true,
        position: 'top',
        color: '#fff',
        fontSize: 14,
      },
    })),
  }
})

// 根据图表类型选择对应的图表选项
const chartOption = computed(() => {
  if (chartType.value === 'line') {
    return lineChartOption.value
  } else if (chartType.value === 'compareBar') {
    return compareBarChartOption.value
  } else {
    return barChartOption.value
  }
})

// 图表引用
const chartRef = ref(null)

// 模态框显示后初始化图表
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      nextTick(() => {
        handleSearch()
      })
    }
  },
)

onMounted(() => {
  // 初始加载
  if (props.modelValue) {
    handleSearch()
  }
})

// 重置查询条件
const resetQuery = () => {
  selectedModule.value = 'fhgbj'
  selecteDept.value = ''
  selectedSubDept.value = ''
  subDeptOptions.value = []
  dateRange.value = [new Date(new Date().getFullYear(), 0, 1), new Date()]
  handleSearch()
}

// 单位选择
const handleDeptChange = async () => {
  console.log('单位选择', selecteDept.value)
  // 清空之前选择的分县局
  selectedSubDept.value = ''
  // 获取分县局列表并等待完成
  await fetchSubDeptList()
  // 然后执行搜索
  handleSearch()
}

// 获取分县局列表
const fetchSubDeptList = async () => {
  if (!selecteDept.value) {
    subDeptOptions.value = []
    return
  }

  subDeptLoading.value = true
  try {
    const result = await analysisModalApi.fetchSubDepts(selecteDept.value)
    console.log('获取分县局列表结果:', result)
    if (Array.isArray(result)) {
      subDeptOptions.value = result.map((item) => ({
        value: item.CODE,
        label: item.DETAIL,
      }))
    } else {
      subDeptOptions.value = []
    }
  } catch (error) {
    console.error('获取分县局列表失败:', error)
    subDeptOptions.value = []
  } finally {
    subDeptLoading.value = false
  }
}

// 分县局选择
const handleSubDeptChange = () => {
  console.log('分县局选择', selectedSubDept.value)
  handleSearch()
}

// 搜索方法
const handleSearch = async () => {
  // 验证日期范围
  if (dateRange.value[0] && dateRange.value[1] && dateRange.value[0] > dateRange.value[1]) {
    ElMessage.error('开始时间不能晚于结束时间')
    return
  }

  loading.value = true
  try {
    // 构建查询参数
    const params = analysisModalApi.buildParams(dateRange.value, '45')

    // 添加单位参数
    if (selectedSubDept.value) {
      // 如果选择了分县局，优先使用分县局代码
      params.dept = selectedSubDept.value
    } else if (selecteDept.value) {
      // 否则使用选择的单位代码
      params.dept = selecteDept.value
    }

    // 检查是否选择了公安选项，如果是则添加 type: "dfga" 参数
    if (selecteDept.value && selecteDept.value.endsWith('_GA')) {
      params.type = 'dfga'
    }

    // 1. 获取图表数据（趋势图数据）
    const trendData = await analysisModalApi.fetchTrendData(selectedModule.value, params)
    console.log('获取到的趋势图数据', trendData)

    // 输出trendData中的所有key，检查是否有xsxzlaData
    console.log('趋势图数据所有key:', Object.keys(trendData))

    // 2. 获取概览数据（同环比详细数据）
    const detailData = await analysisModalApi.fetchDetailData(selectedModule.value, params)
    console.log('获取到的同环比详细数据', detailData)

    // 处理数据
    if (trendData && detailData) {
      // 保存原始数据
      chartData.rawData = trendData

      // 设置x轴数据（两种图表类型都需要）
      const monthData =
        trendData[
          Object.keys(trendData).find(
            (key) => Array.isArray(trendData[key]) && trendData[key].length > 0,
          )
        ] || []

      // 保存原始日期数据
      chartData.originalDates = monthData.map((item) => item.DAYTIME)
      console.log('保存原始日期:', chartData.originalDates)

      chartData.xAxis = monthData.map((item) => {
        const month = new Date(item.DAYTIME).getMonth() + 1
        return `${month}月`
      })

      if (chartType.value === 'line') {
        // 折线图模式 - 处理单选指标的本期/同期数据
        if (selectedIndicator.value) {
          processLineChartData()
        } else if (indicatorOptions.value.length > 0) {
          // 如果没有选中指标但有可选指标，则选择第一个
          selectedIndicator.value = indicatorOptions.value[0].value
          processLineChartData()
        }
      } else if (chartType.value === 'compareBar') {
        // 确保先处理概览数据
        const summaryResult = analysisModalApi.processOverviewData(selectedModule.value, detailData)
        summaryData.currentYear = summaryResult.currentYear
        summaryData.lastYear = summaryResult.lastYear
        summaryData.beforeLastYear = summaryResult.beforeLastYear

        // 选择指标
        if (!selectedIndicator.value && indicatorOptions.value.length > 0) {
          selectedIndicator.value = indicatorOptions.value[0].value
        }

        // 强制处理为同比柱状图格式
        if (selectedIndicator.value) {
          nextTick(() => {
            processCompareBarChartData()
          })
        }
      } else {
        // 柱状图模式 - 使用原来的多指标处理方式
        const chartResult = analysisModalApi.processChartData(selectedModule.value, trendData)
        console.log('处理后的图表数据', chartResult)

        chartData.xAxis = chartResult.xAxis
        chartData.series = chartResult.series

        // 重置图例选择状态 - 初始不选中任何图例，等待用户点击选择
        legendSelectedKeys.value = []
      }

      // 处理概览数据 - 移动到各自的图表类型处理中，确保顺序正确
      if (chartType.value !== 'compareBar') {
        const summaryResult = analysisModalApi.processOverviewData(selectedModule.value, detailData)
        summaryData.currentYear = summaryResult.currentYear
        summaryData.lastYear = summaryResult.lastYear
        summaryData.beforeLastYear = summaryResult.beforeLastYear
      }
    }
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取详情数据
const fetchDetailData = async (type, page = 1) => {
  detailLoading.value = true
  try {
    // 构建查询参数
    const params = analysisModalApi.buildParams(dateRange.value, '45')

    // 添加单位参数
    if (selectedSubDept.value) {
      params.dept = selectedSubDept.value
    } else if (selecteDept.value) {
      params.dept = selecteDept.value
    }

    // 添加详情查询参数
    params.type = type
    params.page = page.toString()
    params.pageSize = detailPageSize.value.toString()

    // 检查是否选择了公安选项，如果是则添加公安类型标识
    if (selecteDept.value && selecteDept.value.endsWith('_GA')) {
      params.gaType = 'dfga'
    }

    const response = await analysisModalApi.fetchDetailData('fhgbj', params, type)
    console.log('获取详情数据结果:', response)

    if (response && response.data) {
      detailData.value = response.data
      detailTotal.value = response.total || 0
      detailCurrentPage.value = page
    } else {
      detailData.value = []
      detailTotal.value = 0
    }
  } catch (error) {
    console.error('获取详情数据失败:', error)
    ElMessage.error('获取详情数据失败')
    detailData.value = []
    detailTotal.value = 0
  } finally {
    detailLoading.value = false
  }
}

// 处理详情点击事件
const handleDetailClick = (item) => {
  console.log('点击详情:', item)

  // 检查是否为可点击的指标
  const indicatorName = item.label
  const type = detailTypeMap[indicatorName]

  if (type) {
    detailType.value = type
    detailModalVisible.value = true
    detailCurrentPage.value = 1
    fetchDetailData(type, 1)
  }
}

// 处理详情模态框分页
const handleDetailPageChange = (page) => {
  fetchDetailData(detailType.value, page)
}

// 关闭详情模态框
const closeDetailModal = () => {
  detailModalVisible.value = false
  detailData.value = []
  detailTotal.value = 0
  detailType.value = ''
}
</script>

<template>
  <!-- 自定义模态框 -->
  <div v-if="modelValue" class="custom-modal-overlay" @click="handleClose">
    <div class="custom-modal" @click="handleContentClick">
      <!-- 标题区域 -->
      <div class="dialog-header">
        <img :src="titlebg" class="title-bg" alt="标题背景" />
        <div class="title-content">
          <img :src="titleIcon" class="title-icon" alt="标题图标" />
          <div class="title-text">态势分析</div>
        </div>
        <div class="close-btn" @click="handleClose">×</div>
      </div>

      <!-- 条件区域 -->
      <div class="condition-area">
        <div class="condition-item">
          <span class="condition-label">模块：</span>
          <el-select
            v-model="selectedModule"
            placeholder="请选择模块"
            size="large"
            class="module-select"
          >
            <el-option
              v-for="item in moduleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>

        <!-- 指标选择（仅在折线图模式下显示） -->
        <div class="condition-item" v-if="chartType === 'line' || chartType === 'compareBar'">
          <span class="condition-label">指标：</span>
          <el-select
            v-model="selectedIndicator"
            placeholder="请选择指标"
            size="large"
            class="indicator-select"
          >
            <el-option
              v-for="item in indicatorOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>

        <!-- 单位选择 -->
        <div class="condition-item">
          <span class="condition-label">单位：</span>
          <el-select
            v-model="selecteDept"
            placeholder="请选择单位"
            size="large"
            class="city-select"
            :clearable="true"
            @change="handleDeptChange"
          >
            <el-option
              v-for="item in cityOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>

        <!-- 分县局选择 -->
        <div class="condition-item">
          <span class="condition-label">分县局：</span>
          <el-select
            v-model="selectedSubDept"
            placeholder="请选择分县局"
            size="large"
            class="city-select"
            :clearable="true"
            :loading="subDeptLoading"
            @change="handleSubDeptChange"
          >
            <el-option
              v-for="item in subDeptOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>

        <div class="condition-item date-condition">
          <span class="condition-label">时间：</span>
          <div class="date-inputs">
            <el-date-picker
              v-model="dateRange[0]"
              type="date"
              placeholder="开始日期"
              size="large"
              class="date-picker-single"
              @change="handleStartDateChange"
            />
            <span class="date-separator">至</span>
            <el-date-picker
              v-model="dateRange[1]"
              type="date"
              placeholder="结束日期"
              size="large"
              class="date-picker-single"
              @change="handleEndDateChange"
            />
            <el-select
              v-model="selectedShortcut"
              placeholder="快捷选择"
              size="large"
              class="date-shortcut"
              :clearable="true"
              @change="handleShortcutChange"
            >
              <el-option
                v-for="(item, index) in shortcuts"
                :key="index"
                :label="item.text"
                :value="index"
              />
            </el-select>
          </div>
        </div>

        <div class="condition-buttons">
          <el-button type="primary" size="default" @click="handleSearch" :loading="loading"
            >搜索</el-button
          >
          <el-button size="default" @click="resetQuery">重置</el-button>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="content-area">
        <!-- 加载指示器 -->
        <div v-if="loading" class="loading-overlay">
          <span>加载中...</span>
        </div>

        <!-- 左侧折线图 -->
        <div class="chart-container">
          <div class="chart-type-tabs">
            <div
              v-for="tab in chartTabs"
              :key="tab.key"
              :class="['chart-type-tab', { active: chartType === tab.key }]"
              @click="chartType = tab.key"
            >
              {{ tab.label }}
            </div>
          </div>
          <div class="chart-visual-area">
            <v-chart
              class="chart-echart"
              :option="chartOption"
              :autoresize="autoresize"
              @legendselectchanged="onLegendSelectChanged"
              ref="chartRef"
            />
          </div>
        </div>

        <!-- 右侧概览数据 -->
        <div class="summary-container">
          <div class="summary-title">概览数据</div>
          <div
            v-for="(yearData, index) in [
              filteredSummaryData.currentYear,
              filteredSummaryData.lastYear,
              filteredSummaryData.beforeLastYear,
            ]"
            :key="index"
            class="year-panel"
          >
            <div class="year-title">{{ yearData.title }}</div>
            <div class="year-data">
              <div v-for="(item, itemIndex) in yearData.data" :key="itemIndex" class="data-row">
                <div class="data-header-row">
                  <div class="data-header-label">{{ item.label }}</div>
                  <!-- 只在当前期(index=0)显示同比环比标题 -->
                  <div v-if="index === 0" class="data-header-right">
                    <div class="compare-item" :class="item.same.startsWith('+') ? 'up' : 'down'">
                      同比
                    </div>
                    <div
                      class="compare-item"
                      :class="item.chain.startsWith('+') ? 'up' : item.chain === '0%' ? '' : 'down'"
                    >
                      环比
                    </div>
                  </div>
                </div>
                <div
                  class="data-value-row"
                  :class="{ clickable: detailTypeMap[item.label] }"
                  @click="handleDetailClick(item)"
                >
                  <div class="data-value">{{ item.value }}</div>
                  <!-- 只在当前期(index=0)显示同比环比数值 -->
                  <div v-if="index === 0" class="data-value-right">
                    <div class="compare-item" :class="item.same.startsWith('+') ? 'up' : 'down'">
                      {{ item.same }}
                    </div>
                    <div
                      class="compare-item"
                      :class="item.chain.startsWith('+') ? 'up' : item.chain === '0%' ? '' : 'down'"
                    >
                      {{ item.chain }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 详情模态框 -->
  <div v-if="detailModalVisible" class="detail-modal-overlay" @click="closeDetailModal">
    <div class="detail-modal" @click.stop>
      <!-- 标题区域 -->
      <div class="detail-header">
        <div class="detail-title">{{ detailTitleMap[detailType] }}</div>
        <div class="detail-close-btn2" @click="closeDetailModal">×</div>
      </div>

      <!-- 内容区域 -->
      <div class="detail-content">
        <!-- 加载指示器 -->
        <div v-if="detailLoading" class="detail-loading">
          <span>加载中...</span>
        </div>

        <!-- 表格区域 -->
        <div v-else class="detail-table-container">
          <div class="table-wrapper">
            <el-table
              :data="detailData"
              style="width: 100%"
              :header-cell-style="{ background: 'rgba(0, 24, 48, 0.8)', color: '#ffffff' }"
              :cell-style="{ background: 'rgba(0, 24, 48, 0.3)', color: '#ffffff' }"
              border
              :max-height="650"
            >
              <el-table-column prop="RYXM" label="姓名" min-width="100" />
              <el-table-column prop="ZJHM" label="证件号码" min-width="150" />
              <el-table-column prop="SFZGR" label="是否中国人" min-width="100" />
              <el-table-column prop="AJBH" label="案件编号" min-width="120" />
              <el-table-column prop="AB" label="案别" min-width="100" />
              <el-table-column prop="SFBJ" label="是否边境" min-width="100" />
              <el-table-column prop="NUM" label="数量" width="80" />
            </el-table>
          </div>

          <!-- 分页 -->
          <div class="detail-pagination">
            <el-pagination
              v-model:current-page="detailCurrentPage"
              :page-size="detailPageSize"
              :total="detailTotal"
              layout="total, prev, pager, next"
              @current-change="handleDetailPageChange"
              background
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(.el-table tr) {
  background-color: unset;
  font-size: 18px;
}

:deep(.el-pagination.is-background .btn-prev:disabled) {
  background-color: unset;
}
/* 自定义模态框样式 */
.custom-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 5vh;
  animation: fadeIn 0.3s ease-out;
}

.custom-modal {
  width: 98%;
  background: #041418;
  border: 1px solid #0dcaf5;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 208, 255, 0.2);
  animation: slideDown 0.4s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 标题区域 */
.dialog-header {
  position: relative;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: start;
}

.title-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.title-content {
  display: flex;
  align-items: center;
  /* margin-left: 20px; */
}

.title-icon {
  width: 48px;
  height: 48px;
  margin-right: 10px;
  z-index: 10;
}

.title-text {
  position: relative;
  font-family: PangMenZhengDao;
  font-size: 24px;
  color: #ffffff;
  letter-spacing: 2.25px;
  text-shadow: 0 2px 4px rgba(36, 158, 255, 0.48);
  font-weight: 400;
}

.close-btn {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 30px;
  color: transparent;
  width: 50px;
  cursor: pointer;
}

/* 条件区域 */
.condition-area {
  display: flex;
  padding: 10px 15px;
  background: rgba(0, 32, 64, 0.5);
  border-bottom: 1px solid rgba(0, 208, 255, 0.3);
  align-items: center;
  flex-wrap: wrap; /* 允许在需要时换行 */
  justify-content: space-between; /* 更改为空间均匀分布 */
  gap: 10px; /* 增加间隙确保元素之间有更多空间 */
  row-gap: 12px; /* 行间距稍大一些 */
}

.condition-item {
  display: flex;
  align-items: center;
  margin-right: 0;
  margin-bottom: 0; /* 移除底部边距，使用row-gap代替 */
  flex: 1; /* 让每个条件项按比例占据空间 */
  min-width: 170px; /* 增加最小宽度，让控件更宽敞 */
  max-width: 240px; /* 增加最大宽度 */
}

/* 日期选择部分特殊处理 */
.date-condition {
  flex: 3; /* 日期部分占据更多空间 */
  min-width: 460px; /* 增加最小宽度 */
  max-width: 650px; /* 增加最大宽度 */
}

.date-inputs {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  width: 100%; /* 宽度占满父容器 */
  gap: 4px; /* 添加细微的间隔 */
}

/* 日期选择器的宽度调整 */
.date-picker-single {
  width: calc(35% - 4px); /* 日期选择器占35%宽度 */
  min-width: 120px;
}

.date-shortcut {
  width: calc(30% - 4px); /* 快捷选择占30%宽度 */
  min-width: 100px;
}

.date-separator {
  margin: 0;
  padding: 0 4px;
  color: #9be5ff;
  flex-shrink: 1;
}

.condition-label {
  font-size: 14px; /* 稍微调小字体大小 */
  margin-right: 6px;
  color: #9be5ff;
  white-space: nowrap;
}

.module-select,
.indicator-select,
.city-select,
.date-picker-single,
.date-shortcut {
  width: 100%; /* 宽度设为100%，填充父容器可用空间 */
  min-width: 100px; /* 设置最小宽度 */
}

.date-picker-single {
  width: 140px;
}

.date-shortcut {
  width: 120px;
}

.date-separator {
  margin: 0 5px;
  color: #9be5ff;
}

.condition-buttons {
  display: flex;
  gap: 6px;
  align-items: center;
  margin-left: 0; /* 不再自动移到最右边 */
  flex-shrink: 0; /* 防止按钮组被压缩 */
  min-width: 140px; /* 按钮组最小宽度 */
}

/* 主要内容区域 */
.content-area {
  display: flex;
  padding: 20px;
  height: 600px;
  position: relative;
}

.chart-container {
  flex: 3;
  height: 100%;
  background: rgba(0, 24, 48, 0.3);
  border: 1px solid rgba(0, 208, 255, 0.2);
  border-radius: 4px;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.summary-container {
  flex: none;
  width: 280px;
  margin-left: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  overflow-y: auto;
  scrollbar-width: 3px;
  scrollbar-color: #2c343b #10202a;
}

/* 滚动条美化（webkit） */
.summary-container::-webkit-scrollbar {
  width: 3px;
  background: transparent;
  border-radius: 4px;
}
.summary-container::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 4px;
}
.summary-container::-webkit-scrollbar-thumb:hover {
  background: transparent;
}

.summary-title {
  font-size: 20px;
  color: #ffffff;
  font-weight: bold;
  margin-bottom: 10px;
}

.year-panel {
  background: rgba(0, 24, 48, 0.3);
  border: 1px solid rgba(0, 208, 255, 0.2);
  border-radius: 4px;
  padding: 10px 10px 8px 10px;
}

.year-title {
  font-size: 18px;
  font-weight: bold;
  color: #e5faff;
  margin-bottom: 10px;
}

.year-data {
  width: 100%;
}
.data-row {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 6px;
  width: 100%;
}
.data-header-row {
  display: flex;
  flex-direction: row;
  width: 100%;
  align-items: center;
  font-size: 14px;
  color: #9be5ff;
  font-weight: 500;
  margin-bottom: 2px;
}
.data-header-label {
  min-width: 80px;

  font-size: 16px;

  flex: 1;
  text-align: left;
  font-family: MicrosoftYaHei;

  color: #00dfff;
  letter-spacing: 0;
  font-weight: 600;
}
.data-header-right {
  display: flex;
  gap: 12px;
  flex: none;
}
.data-value-row {
  display: flex;
  flex-direction: row;
  width: 100%;
}
.data-value {
  min-width: 80px;
  width: 80px;
  font-size: 20px;
  font-weight: bold;
  color: #ffffff;
  text-align: left;
  flex: 1;
}
.data-value-right {
  display: flex;
  gap: 12px;
  flex: none;
}
.compare-item {
  font-size: 16px;
  min-width: 60px;
  text-align: right;
  font-weight: 700;
  margin-left: 0;
}
.compare-item.up {
  color: #ff0000;
}
.compare-item.down {
  color: #00ffcc;
}

/* 加载指示器 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #e5faff;
  z-index: 10;
}

.loading-icon {
  font-size: 24px;
  margin-bottom: 8px;
  animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

:deep(.el-select__wrapper.el-tooltip__trigger.el-tooltip__trigger),
:deep(
  .el-date-editor.el-date-editor--daterange.el-input__wrapper.el-range-editor.el-range-editor--large.date-picker.el-tooltip__trigger.el-tooltip__trigger
) {
  background: transparent;
  color: #9be5ff;
  font-size: 16px;
}
:deep(.el-range-input) {
  color: #9be5ff;
  font-size: 16px;
}
:deep(.el-select__selected-item.el-select__placeholder) {
  color: #9be5ff;
}
/* 自定义el-select和el-date-picker样式 */
:deep(.el-select .el-input__wrapper),
:deep(.el-date-editor .el-input__wrapper) {
  background: transparent;
  box-shadow: none;
  border: 1px solid rgba(0, 208, 255, 0.5);
}

:deep(.el-select .el-input__inner),
:deep(.el-date-editor .el-input__inner) {
  color: #ffffff;
}

:deep(.el-select .el-input__suffix),
:deep(.el-date-editor .el-range__icon) {
  color: #9be5ff;
}

:deep(.el-range-separator),
:deep(.el-icon) {
  color: #9be5ff;
}

/* 下拉菜单样式 */
:deep(.el-select__popper) {
  background: #041418 !important;
  border: 1px solid #0dcaf5 !important;
}

:deep(.el-select-dropdown__item) {
  color: #ffffff;
}

:deep(.el-select-dropdown__item.hover),
:deep(.el-select-dropdown__item:hover) {
  background-color: rgba(0, 208, 255, 0.2);
}

:deep(.el-select-dropdown__item.selected) {
  background-color: rgba(0, 208, 255, 0.3);
  color: #9be5ff;
}

/* 日期选择器弹出框样式 */
:deep(.el-picker__popper) {
  background: #041418 !important;
  border: 1px solid #0dcaf5 !important;
}

:deep(.el-date-table td) {
  color: #ffffff;
}

:deep(.el-date-table td.current:not(.disabled)) {
  background-color: rgba(0, 208, 255, 0.3);
  color: #ffffff;
}

:deep(.el-date-table td.available:hover) {
  color: #9be5ff;
}

/* 按钮样式 */
:deep(.el-button) {
  background: transparent;
  border: 1px solid #0dcaf5;
  color: #9be5ff;
  padding: 8px 14px;
  font-size: 14px;
}

:deep(.el-button--primary) {
  background: rgba(0, 127, 255, 0.3);
}

:deep(.el-button--primary:hover) {
  background: rgba(0, 127, 255, 0.5);
}

:deep(.el-button:hover) {
  border-color: #ffffff;
  color: #ffffff;
}

/* 图表类型tab样式 */
.chart-type-tabs {
  display: flex;
  width: 300px;
  margin: 0 0 8px 0;
  border: 1px solid rgba(36, 124, 193, 0.7);
  border-radius: 2px;
  overflow: hidden;
  background: #061a2c;
  position: relative;
  left: 0;
  top: 0;
  z-index: 10;
  height: 32px;
}
.chart-type-tab {
  flex: 1;
  text-align: center;
  font-size: 13px;
  color: #bfc9d7;
  padding: 4px 2px;
  background: rgba(55, 134, 253, 0.1);
  cursor: pointer;
  border-right: 1px solid rgba(36, 124, 193, 0.7);
  font-family: PingFangSC-Medium;
  font-weight: 500;
  transition:
    background 0.2s,
    color 0.2s;
  line-height: 24px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  min-width: 80px;
}
.chart-type-tab:last-child {
  border-right: none;
}
.chart-type-tab.active {
  color: #fff;
  background-image: linear-gradient(180deg, rgba(24, 117, 213, 0) 0%, #247cc1 100%);
  border: 1px solid rgba(36, 124, 193, 0.7);
  border-radius: 2px 0 0 2px;
  font-weight: bold;
}
.chart-type-tab:not(.active):hover {
  background-color: rgba(0, 176, 255, 0.2);
}

.chart-container {
  flex: 3;
  height: 100%;
  background: rgba(0, 24, 48, 0.3);
  border: 1px solid rgba(0, 208, 255, 0.2);
  border-radius: 4px;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.chart-type-tabs {
  flex-shrink: 0;
}

.chart-visual-area {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.chart-visual-area .chart-echart {
  flex: 1;
  min-height: 0;
}

/* 快捷选择下拉框样式 */
.date-shortcut {
  margin-left: 5px;
}

.date-shortcut :deep(.el-select__wrapper) {
  background: transparent;
  box-shadow: none;
  border: 1px solid rgba(0, 208, 255, 0.5);
}

.date-shortcut :deep(.el-select__placeholder) {
  color: #9be5ff;
}

.detail-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 5vh;
  animation: fadeIn 0.3s ease-out;
}

.detail-modal {
  width: 98%;
  min-height: 500px;
  max-height: 90vh;
  background: #041418;
  border: 1px solid #0dcaf5;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 208, 255, 0.2);
  animation: slideDown 0.4s ease-out;
  display: flex;
  flex-direction: column;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 标题区域 */
.detail-header {
  position: relative;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: start;
  flex-shrink: 0;
  padding: 0 20px;
  background: rgba(0, 24, 48, 0.5);
  border-bottom: 1px solid rgba(0, 208, 255, 0.3);
}

.detail-title {
  position: relative;
  font-family: PangMenZhengDao;
  font-size: 20px;
  color: #ffffff;
  letter-spacing: 2px;
  text-shadow: 0 2px 4px rgba(36, 158, 255, 0.48);
  font-weight: 400;
}

.detail-close-btn2 {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24px;
  color: #ffffff;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.detail-close-btn2:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 内容区域 */
.detail-content {
  padding: 20px;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.detail-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #e5faff;
}

.detail-table-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-wrapper {
  flex: 1;
  min-height: 0;
  margin-bottom: 20px;
  overflow: hidden;
}

.detail-pagination {
  flex-shrink: 0;
  text-align: center;
  padding: 10px 0;
  background: rgba(0, 24, 48, 0.3);
  border-top: 1px solid rgba(0, 208, 255, 0.2);
  margin: 0 -20px -20px -20px;
  padding: 15px 20px;
}

/* 可点击行样式 */
.data-value-row.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.data-value-row.clickable:hover {
  background: rgba(0, 208, 255, 0.1);
  border-radius: 4px;
  transform: translateX(5px);
}

.data-value-row.clickable:hover .data-value {
  color: #00dfff;
}

/* Element Plus 表格样式覆盖 */
:deep(.el-table) {
  background: transparent;
  color: #ffffff;
}

:deep(.el-table th) {
  background: rgba(0, 24, 48, 0.8) !important;
  color: #ffffff !important;
  border-color: rgba(0, 208, 255, 0.3) !important;
}

:deep(.el-table td) {
  background: rgba(0, 24, 48, 0.3) !important;
  color: #ffffff !important;
  border-color: rgba(0, 208, 255, 0.2) !important;
}

:deep(.el-table--border) {
  border-color: rgba(0, 208, 255, 0.3) !important;
}

/* Element Plus 分页样式覆盖 */
:deep(.el-pagination) {
  color: #ffffff;
}

:deep(.el-pagination .el-pager li) {
  background: rgba(0, 24, 48, 0.5);
  color: #ffffff;
  border: 1px solid rgba(0, 208, 255, 0.3);
}

:deep(.el-pagination .el-pager li.is-active) {
  background: rgba(0, 208, 255, 0.3);
  color: #ffffff;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  background: rgba(0, 24, 48, 0.5);
  color: #ffffff;
  border: 1px solid rgba(0, 208, 255, 0.3);
}

:deep(.el-pagination .el-pagination__total) {
  color: #ffffff;
}
</style>
