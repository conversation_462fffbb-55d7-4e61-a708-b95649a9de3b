import api from './index'
import { useQueryConditionStore } from '@/stores/queryCondition'

/**
 * 示例API调用
 * 该文件展示如何在Vue组件中使用API请求
 */

// 例子1：基础用法 - 获取用户列表
export const getUserListExample = async () => {
  try {
    // 直接调用API获取用户列表
    const result = await api.user.getList({ page: 1, size: 10 })
    console.log('用户列表:', result)
    return result
  } catch (error) {
    console.error('获取用户列表失败:', error)
    return null
  }
}

// 例子2：结合查询条件store - 获取通行记录
export const getPassageRecordsWithStore = async () => {
  try {
    // 获取查询条件store
    const queryStore = useQueryConditionStore()

    // 使用store中的查询参数发起请求
    const result = await api.passage.getList(queryStore.queryParams)
    console.log('通行记录:', result)
    return result
  } catch (error) {
    console.error('获取通行记录失败:', error)
    return null
  }
}

// 例子3：处理复杂参数 - 批量处理预警信息
export const batchProcessWarningsExample = async (ids, status, remarks) => {
  try {
    if (!ids || ids.length === 0) {
      console.error('缺少预警ID')
      return null
    }

    const result = await api.warning.batchProcess(ids, {
      status,
      remarks,
      operateTime: new Date().toISOString(),
    })

    console.log('批量处理预警结果:', result)
    return result
  } catch (error) {
    console.error('批量处理预警失败:', error)
    return null
  }
}

// 例子4：完整的CRUD操作 - 用户管理
export const userCrudExample = {
  // 查询用户列表
  getList: async (params) => {
    try {
      return await api.user.getList(params)
    } catch (error) {
      console.error('查询用户列表失败:', error)
      return { records: [], total: 0 }
    }
  },

  // 查询单个用户
  getDetail: async (id) => {
    try {
      return await api.user.getDetail(id)
    } catch (error) {
      console.error(`查询用户[${id}]详情失败:`, error)
      return null
    }
  },

  // 创建用户
  create: async (data) => {
    try {
      return await api.user.create(data)
    } catch (error) {
      console.error('创建用户失败:', error)
      return null
    }
  },

  // 更新用户
  update: async (id, data) => {
    try {
      return await api.user.update(id, data)
    } catch (error) {
      console.error(`更新用户[${id}]失败:`, error)
      return null
    }
  },

  // 删除用户
  delete: async (id) => {
    try {
      return await api.user.delete(id)
    } catch (error) {
      console.error(`删除用户[${id}]失败:`, error)
      return false
    }
  },
}

// 导出所有示例
export default {
  getUserListExample,
  getPassageRecordsWithStore,
  batchProcessWarningsExample,
  userCrudExample,
}
