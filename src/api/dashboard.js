import { post } from '@/utils/http'
import { useQueryConditionStore } from '@/stores/queryCondition'
import dayjs from 'dayjs'

/**
 * 边海防仪表盘数据接口
 * 提供与后端api.md文档中定义的接口交互功能
 */

// 处理查询参数，添加时间范围和部门信息
const buildParams = (dept = '45', extraParams = {}) => {
  const queryStore = useQueryConditionStore()

  // 获取日期范围参数
  let startTime = ''
  let endTime = ''

  if (queryStore.dateRange && queryStore.dateRange.length === 2) {
    startTime = dayjs(queryStore.dateRange[0]).format('YYYY-MM-DD HH:mm:ss')
    endTime = dayjs(queryStore.dateRange[1]).format('YYYY-MM-DD HH:mm:ss')
  } else {
    // 默认查询最近30天
    endTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    startTime = dayjs().subtract(30, 'day').format('YYYY-MM-DD HH:mm:ss')
  }
  console.log('queryStore.dept', queryStore.dept)
  return {
    startTime,
    endTime,
    dept: queryStore.dept || dept,
    ...extraParams,
  }
}

/**
 * 妨害国边境违法犯罪数据
 * @param {String} dept 部门代码，默认为全省(45)
 * @returns {Promise<Object>} 处理后的数据
 */
export const getFhgbjData = async (dept = '45') => {
  try {
    const params = buildParams(dept)
    const data = await post('/api/dashboard/fhgbjData', params)

    // 返回处理后的数据
    return {
      // 刑事立案相关数据
      xsla: {
        value: data.XSLAS || 0,
        tbStatus: data.XSLASTBZT || 0, // 同比状态
        ybStatus: data.XSLASYBZT || 0, // 环比状态
        tbValue: data.XSLASTB || '0.00%', // 同比值
        ybValue: data.XSLASYB || '0.00%', // 环比值
      },
      // 刑拘人数相关数据
      xjrs: {
        value: data.XJRS || 0,
        tbStatus: data.XJRSTBZT || 0,
        ybStatus: data.XJRSYBZT || 0,
        tbValue: data.XJRSTB || '0.00%',
        ybValue: data.XJRSYB || '0.00%',
      },
      // 行政立案相关数据
      xzla: {
        value: data.XZLAS || 0,
        tbStatus: data.XZLASTBZT || 0,
        ybStatus: data.XZLASYBZT || 0,
        tbValue: data.XZLASTB || '0.00%',
        ybValue: data.XZLASYB || '0.00%',
      },
      // 处罚人数相关数据
      cfrs: {
        value: data.CFRS || 0,
        tbStatus: data.CFRSTBZT || 0,
        ybStatus: data.CFRSYBZT || 0,
        tbValue: data.CFRSTB || '0.00%',
        ybValue: data.CFRSYB || '0.00%',
      },
      // 组织相关数据（无环比）
      zz: {
        value: data.ZZ || 0,
        tbStatus: data.ZZTBZT || 0,
        tbValue: data.ZZTB || '0.00%',
      },
      // 运送相关数据（无环比）
      ys: {
        value: data.YS || 0,
        tbStatus: data.YSTBZT || 0,
        tbValue: data.YSTB || '0.00%',
      },
      // 偷越相关数据（无环比）
      ty: {
        value: data.TY || 0,
        tbStatus: data.TYTBZT || 0,
        tbValue: data.TYTB || '0.00%',
      },
      // 行政处罚(中国人)
      xzcf: {
        value: data.CFRSZGR || 0,
        tbStatus: data.CFRSZGRTBZT || 0,
        tbValue: data.CFRSZGRTB || '0.00%',
      },
      // 偷越中国人相关数据（无环比）
      tyzgr: {
        value: data.TYZGR || 0,
        tbStatus: data.TYZGRTBZT || 0,
        tbValue: data.TYZGRTB || '0.00%',
      },
      // 刑事+行政相关数据
      xsxz: {
        value: data.XSXZLAS || 0,
        tbStatus: data.XSXZLASTBZT || 0,
        ybStatus: data.XSXZLASYBZT || 0,
        tbValue: data.XSXZLASTB || '0.00%',
        ybValue: data.XSXZLASYB || '0.00%',
      },
      // 刑拘+处罚相关数据
      xjcf: {
        value: data.XJCFRS || 0,
        tbStatus: data.XJCFRSTBZT || 0,
        ybStatus: data.XJCFRSYBZT || 0,
        tbValue: data.XJCFRSTB || '0.00%',
        ybValue: data.XJCFRSYB || '0.00%',
      },
      // 原始数据
      raw: data,
    }
  } catch (error) {
    console.error('获取妨害国边境违法犯罪数据失败:', error)
    return null
  }
}

/**
 * 走私案件情况数据
 * @param {String} dept 部门代码，默认为全省(45)
 * @returns {Promise<Object>} 处理后的数据
 */
export const getZsajqkData = async (dept = '45') => {
  try {
    const params = buildParams(dept)
    const data = await post('/api/dashboard/zsajqkData', params)

    // 返回处理后的数据
    return {
      // 立案数
      LAS: data.LAS || 0,
      LASTB: data.LASTB || '0%',
      LASYB: data.LASYB || '0%',
      LASTBZT: data.LASTBZT || 0,
      LASYBZT: data.LASYBZT || 0,

      // 抓获
      ZH: data.ZH || 0,
      ZHTB: data.ZHTB || '0%',
      ZHYB: data.ZHYB || '0%',
      ZHTBZT: data.ZHTBZT || 0,
      ZHYBZT: data.ZHYBZT || 0,

      // 刑拘
      JL: data.JL || 0,
      JLTB: data.JLTB || '0%',
      JLYB: data.JLYB || '0%',
      JLTBZT: data.JLTBZT || 0,
      JLYBZT: data.JLYBZT || 0,

      // 逮捕
      DB: data.DB || 0,
      DBTB: data.DBTB || '0%',
      DBYB: data.DBYB || '0%',
      DBTBZT: data.DBTBZT || 0,
      DBYBZT: data.DBYBZT || 0,

      // 移诉
      YS: data.YS || 0,
      YSTB: data.YSTB || '0%',
      YSYB: data.YSYB || '0%',
      YSTBZT: data.YSTBZT || 0,
      YSYBZT: data.YSYBZT || 0,

      // 取保
      QB: data.QB || 0,
      QBTB: data.QBTB || '0%',
      QBYB: data.QBYB || '0%',
      QBTBZT: data.QBTBZT || 0,
      QBYBZT: data.QBYBZT || 0,

      // 原始数据
      raw: data,
    }
  } catch (error) {
    console.error('获取走私案件情况数据失败:', error)
    return null
  }
}

/**
 * 边境涉毒涉枪犯罪数据
 * @param {String} dept 部门代码，默认为全省(45)
 * @returns {Promise<Object>} 处理后的数据
 */
export const getBjsdsqfzData = async (dept = '45') => {
  try {
    const params = buildParams(dept)
    const data = await post('/api/dashboard/bjsdsqfzData', params)

    // 返回处理后的数据
    return {
      // 涉毒相关
      sd: {
        // 涉毒刑事立案数
        xslas: data.SDXSLAS || 0,
        // 抓获人员数
        zhrys: data.ZHRYS || 0,
        // 涉毒行政案件数
        xzajs: data.XDXZAJS || 0,
        // 查处吸毒人员数
        ccxdrys: data.CCXDRYS || 0,
      },
      // 涉枪相关
      sq: {
        // 涉枪立案数
        las: data.SQLAS || 0,
        // 涉枪抓获人数
        zhs: data.SQZHS || 0,
        // 缴获枪支数
        jhqzs: data.JHQZS || 0,
      },
      // 原始数据
      raw: data,
    }
  } catch (error) {
    console.error('获取边境涉毒涉枪犯罪数据失败:', error)
    return null
  }
}

/**
 * 各地妨害国边境各市情况
 * @param {String} dept 部门代码，默认为全省(45)
 * @param {Object} extraParams 额外参数
 * @returns {Promise<Array>} 处理后的数据
 */
export const getGdfhgbjData = async (dept = '45', extraParams = {}) => {
  try {
    const params = buildParams(dept, extraParams)
    const data = await post('/api/dashboard/gdfhgbjData', params)

    // 处理数据，确保每项都包含必要的字段
    const processedData = Array.isArray(data)
      ? data.map((item) => ({
          deptName: item.DEPTNAME || '',
          deptCode: item.DEPTCODE || '',
          ajs: item.AJS || 0, // 案件数
          xjrs: item.XJRS || 0, // 刑拘人数
          db: item.DB || 0, // 逮捕
          ysqs: item.YSQS || 0, // 移送起诉
          num: item.NUM || 0, // 序号
          raw: item, // 原始数据
        }))
      : []

    return processedData
  } catch (error) {
    console.error('获取各地妨害国边境各市情况数据失败:', error)
    return []
  }
}

/**
 * 走私类各市情况
 * @param {String} dept 部门代码，默认为全省(45)
 * @param {Object} extraParams 额外参数
 * @returns {Promise<Array>} 处理后的数据
 */
export const getGdzslData = async (dept = '45', extraParams = {}) => {
  try {
    const params = buildParams(dept, extraParams)
    const data = await post('/api/dashboard/gdzslData', params)

    // 处理数据，确保每项都包含必要的字段
    const processedData = Array.isArray(data)
      ? data.map((item) => ({
          deptName: item.DEPTNAME || '',
          deptCode: item.DEPTCODE || '',
          ajs: item.AJS || 0, // 案件数
          qzcss: item.QZCSS || 0, // 采取刑事强制措施人数
          db: item.DB || 0, // 逮捕
          ysqs: item.YSQS || 0, // 移送起诉
          num: item.NUM || 0, // 序号
          raw: item, // 原始数据
        }))
      : []

    return processedData
  } catch (error) {
    console.error('获取走私类各市情况数据失败:', error)
    return []
  }
}

/**
 * 毒品类各市情况
 * @param {String} dept 部门代码，默认为全省(45)
 * @param {Object} extraParams 额外参数
 * @returns {Promise<Array>} 处理后的数据
 */
export const getDplgsqkData = async (dept = '45', extraParams = {}) => {
  try {
    const params = buildParams(dept, extraParams)
    // 注意：该接口与getGdfhgbjData使用相同的URL，但参数和用途不同
    const data = await post('/api/dashboard/dplgsData', params)

    // 处理数据，确保每项都包含必要的字段
    const processedData = Array.isArray(data)
      ? data.map((item) => ({
          deptName: item.DEPTNAME || '',
          deptCode: item.DEPTCODE || '',
          las: item.LAS || 0, // 立案数
          zhrys: item.ZHRYS || 0, // 抓获人员数
          num: item.NUM || 0, // 序号
          raw: item, // 原始数据
        }))
      : []

    return processedData
  } catch (error) {
    console.error('获取毒品类各市情况数据失败:', error)
    return []
  }
}

/**
 * 各地出入境类各市情况
 * @param {String} dept 部门代码，默认为全省(45)
 * @param {Object} extraParams 额外参数
 * @returns {Promise<Array>} 处理后的数据
 */
export const getGdcrjData = async (dept = '45', extraParams = {}) => {
  try {
    const params = buildParams(dept, extraParams)
    const data = await post('/api/dashboard/gdcrjlData', params)
    // 处理数据，确保每项都包含必要的字段
    const processedData = Array.isArray(data)
      ? data.map((item) => ({
          deptName: item.DEPTNAME || '',
          deptCode: item.DEPTCODE || '',
          las: item.LAS || 0,
          zhzs: item.ZHZS || 0,
          xjzs: item.XJZS || 0,
          num: item.NUM || 0,
          raw: item, // 原始数据
        }))
      : []
    return processedData
  } catch (error) {
    console.error('获取各地出入境类各市情况数据失败:', error)
    return []
  }
}

export const getfhgbjTs = async (dept = '45', tsTime) => {
  try {
    const params = buildParams(dept)
    params.tsTime = tsTime
    const data = await post('/api/dashboard/fhgbjTs', params)
    return data
  } catch (error) {
    console.error('获取妨害国边境态势数据失败:', error)
    return null
  }
}

export const getZsajqkTs = async (dept = '45', tsTime) => {
  try {
    const params = buildParams(dept)
    params.tsTime = tsTime
    const data = await post('/api/dashboard/zsajqkTs', params)
    return data
  } catch (error) {
    console.error('获取走私类态势数据失败:', error)
    return null
  }
}

export const getBjsdsqfzTs = async (dept = '45', tsTime) => {
  try {
    const params = buildParams(dept)
    params.tsTime = tsTime
    const data = await post('/api/dashboard/bjsdsqfzTs', params)
    return data
  } catch (error) {
    console.error('获取边境涉毒涉枪态势数据失败:', error)
    return null
  }
}

/**
 * 获取人员抓获数据（地图显示用）
 * @param {String} dept 部门代码，默认为全省(45)
 * @returns {Promise<Object>} 处理后的数据
 */
export const getRyzsData = async (dept = '45') => {
  try {
    const params = buildParams(dept)
    const data = await post('/api/dashboard/mapData', params)

    // 处理返回的数据
    const processedData = {
      // 总人数（所有ZS字段的总和）
      totalCount: data.reduce((sum, item) => sum + (item.ZS || 0), 0),
      // 各类案件人员数（所有地市的总和）
      borderViolation: data.reduce((sum, item) => sum + (item.FHGBJZHS || 0), 0), // 妨害国边境
      smuggling: data.reduce((sum, item) => sum + (item.ZSZHS || 0), 0), // 走私
      drugRelated: data.reduce((sum, item) => sum + (item.SDZHS || 0), 0), // 涉毒
      weaponRelated: data.reduce((sum, item) => sum + (item.SQZHS || 0), 0), // 涉枪
      // 各地市数据
      cityData: data.map((item) => ({
        code: item.DEPTCODE ? item.DEPTCODE.substring(0, 4) : '',
        name: item.DEPTNAME || '',
        value: item.ZS || 0,
        // 每个地市的详细数据
        detail: {
          borderViolation: item.FHGBJZHS || 0, // 妨害国边境
          smuggling: item.ZSZHS || 0, // 走私
          drugRelated: item.SDZHS || 0, // 涉毒
          weaponRelated: item.SQZHS || 0, // 涉枪
          total: item.ZS || 0, // 总数
        },
      })),
      // 原始数据
      raw: data,
    }

    return processedData
  } catch (error) {
    console.error('获取人员抓获数据失败:', error)
    return {
      totalCount: 0,
      borderViolation: 0,
      smuggling: 0,
      drugRelated: 0,
      weaponRelated: 0,
      cityData: [],
      raw: {},
    }
  }
}

/**
 * 获取口岸边防检查情况数据
 * @param {String} dept 部门代码，默认为全省(45)
 * @returns {Promise<Object>} 处理后的数据
 */
export const getKabfjcData = async (dept = '45') => {
  try {
    const params = buildParams(dept)
    const data = await post('/api/dashboard/kabfjcqkData', params)

    return data
  } catch (error) {
    console.error('获取口岸边防检查数据失败:', error)
    return {}
  }
}

/**
 * 获取边境检查站查缉情况数据
 * @param {String} dept 部门代码，默认为全省(45)
 * @returns {Promise<Object>} 处理后的数据
 */
export const getBjjczcjqkData = async (dept = '45') => {
  try {
    const params = buildParams(dept)
    const data = await post('/api/dashboard/getBjjczcjqkData', params)

    return data
  } catch (error) {
    console.error('获取边境检查站查缉情况数据失败:', error)
    return {}
  }
}

/**
 * 获取口岸边防检查情况态势数据
 * @param {String} dept 部门代码，默认为全省(45)
 * @param {String} tsTime 时间范围："12"表示近12个月数据，"24"表示前12个月数据
 * @returns {Promise<Object>} 处理后的数据
 */
export const getKabfjcqkTs = async (dept = '45', tsTime = '12') => {
  try {
    const params = buildParams(dept)
    params.tsTime = tsTime
    const data = await post('/api/dashboard/kabfjcqkTs', params)
    return data
  } catch (error) {
    console.error('获取口岸边防检查态势数据失败:', error)
    return null
  }
}

/**
 * 获取边境检查站查缉情况态势数据
 * @param {String} dept 部门代码，默认为全省(45)
 * @param {String} tsTime 时间范围："12"表示近12个月数据，"24"表示前12个月数据
 * @returns {Promise<Object>} 处理后的数据
 */
export const getBjjczcjqkTs = async (dept = '45', tsTime = '12') => {
  try {
    const params = buildParams(dept)
    params.tsTime = tsTime
    const data = await post('/api/dashboard/bjjczcjqkTs', params)
    return data
  } catch (error) {
    console.error('获取边境检查站查缉情况态势数据失败:', error)
    return null
  }
}

/**
 * 获取出入境证件办理情况数据
 * @param {String} dept 部门代码，默认为全省(45)
 * @returns {Promise<Object>} 处理后的数据
 */
export const getWgrsqSpdwTj = async (dept = '45') => {
  try {
    const params = buildParams(dept)
    const data = await post('/api/dashboard/crjzjblqkData', params)
    return data
  } catch (error) {
    console.error('获取出入境证件办理情况数据失败:', error)
    return {}
  }
}

/**
 * 获取出入境证件办理情况态势数据
 * @param {String} dept 部门代码，默认为全省(45)
 * @param {String} tsTime 时间范围："12"表示近12个月数据，"24"表示前12个月数据
 * @param {String} type 数据类型：zgrbzs-中国人办证数，wgrbzs-外国人办证数，byqfrys-不予签发人员，wgrlzdj-外国人临住登记
 * @returns {Promise<Object>} 处理后的数据
 */
export const getCrjzjblqkTs = async (dept = '45', tsTime = '12', type = 'zgrbzs') => {
  try {
    const params = buildParams(dept)
    params.tsTime = tsTime
    params.type = type
    const data = await post('/api/dashboard/crjzjblqkTs', params)
    // 接口返回的数据结构不同，需要做一些处理来统一格式
    // 将返回的type对应的数组放在对应的key下
    const result = {}
    if (data && data[type]) {
      result[type] = data[type]
    }
    return result
  } catch (error) {
    console.error(`获取出入境证件办理情况态势数据(${type})失败:`, error)
    return {}
  }
}

/**
 * 获取出入境证件办理情况态势完整数据（合并四个接口数据）
 * @param {String} dept 部门代码，默认为全省(45)
 * @param {String} tsTime 时间范围："12"表示近12个月数据，"24"表示前12个月数据
 * @returns {Promise<Object>} 处理后的数据
 */
export const getCrjzjblqkTsAll = async (dept = '45', tsTime = '12') => {
  try {
    // 并发请求四个数据类型
    const [zgrbzsData, wgrbzsData, byqfrysData, wgrlzdjData] = await Promise.all([
      getCrjzjblqkTs(dept, tsTime, 'zgrbzs'),
      getCrjzjblqkTs(dept, tsTime, 'wgrbzs'),
      getCrjzjblqkTs(dept, tsTime, 'byqfrys'),
      getCrjzjblqkTs(dept, tsTime, 'wgrlzdj'),
    ])

    // 合并四个接口的结果
    return {
      ...zgrbzsData,
      ...wgrbzsData,
      ...byqfrysData,
      ...wgrlzdjData,
    }
  } catch (error) {
    console.error('获取出入境证件办理情况态势完整数据失败:', error)
    return {}
  }
}

/**
 * 获取通信信号分析数据
 * @param {String} dept 部门代码，默认为全省(45)
 * @returns {Promise<Object>} 通信信号分析数据
 */
export const getRydbData = async (dept = '45') => {
  try {
    const params = buildParams(dept)
    const data = await post('/api/dashboard/rydbData', params)
    return data
  } catch (error) {
    console.error('获取通信信号分析数据失败:', error)
    return null
  }
}

export default {
  getFhgbjData,
  getZsajqkData,
  getBjsdsqfzData,
  getGdfhgbjData,
  getGdzslData,
  getDplgsqkData,
  getfhgbjTs,
  getZsajqkTs,
  getBjsdsqfzTs,
  getRyzsData,
  getGdcrjData,
  getKabfjcData,
  getBjjczcjqkData,
  getKabfjcqkTs,
  getBjjczcjqkTs,
  getWgrsqSpdwTj,
  getCrjzjblqkTs,
  getCrjzjblqkTsAll,
  getRydbData,
}
