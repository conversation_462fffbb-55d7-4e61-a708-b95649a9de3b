import { post, put, get, del } from '@/utils/http'
import { useQueryConditionStore } from '@/stores/queryCondition'
import dayjs from 'dayjs'

/**
 * 督导盯办相关接口
 * 提供与后端督导盯办相关的接口交互功能
 */

// 处理查询参数，添加时间范围和部门信息
const buildParams = (dept = '45', extraParams = {}) => {
  const queryStore = useQueryConditionStore()

  // 获取日期范围参数
  let startTime = ''
  let endTime = ''

  if (queryStore.dateRange && queryStore.dateRange.length === 2) {
    startTime = dayjs(queryStore.dateRange[0]).format('YYYY-MM-DD HH:mm:ss')
    endTime = dayjs(queryStore.dateRange[1]).format('YYYY-MM-DD HH:mm:ss')
  } else {
    // 默认查询最近30天
    endTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    startTime = dayjs().subtract(30, 'day').format('YYYY-MM-DD HH:mm:ss')
  }

  return {
    startTime,
    endTime,
    dept: queryStore.dept || dept,
    ...extraParams,
  }
}

/**
 * 新增督导盯办任务
 * @param {Object} params 任务参数
 * @returns {Promise<Object>} 处理后的数据
 */
export const addSupervisionTask = async (params) => {
  try {
    const data = await post('/api/dddb/b_bhgkjsc_dddbrw', params)
    return data
  } catch (error) {
    console.error('新增督导盯办任务失败:', error)
    return null
  }
}

/**
 * 修改督导盯办任务
 * @param {String} systemid 任务ID
 * @param {Object} params 任务参数
 * @returns {Promise<Object>} 处理后的数据
 */
export const updateSupervisionTask = async (systemid, params) => {
  try {
    const data = await put(`/api/dddb/b_bhgkjsc_dddbrw/${systemid}`, params)
    return data
  } catch (error) {
    console.error('修改督导盯办任务失败:', error)
    return null
  }
}

/**
 * 删除督导盯办任务
 * @param {String} systemid 任务ID
 * @returns {Promise<Object>} 处理后的数据
 */
export const deleteSupervisionTask = async (systemid) => {
  try {
    const data = await del(`/api/dddb/b_bhgkjsc_dddbrw/${systemid}`)
    return data
  } catch (error) {
    console.error('删除督导盯办任务失败:', error)
    return null
  }
}

/**
 * 获取任务概览数据
 * @returns {Promise<Object>} 处理后的数据
 */
export const getTaskOverview = async () => {
  try {
    const data = await get('/api/dddb/b_bhgkjsc_dddbrw/getRwgl')
    return {
      totalTasks: data.RWZS || 0, // 任务总数
      completedTasks: data.WCS || 0, // 完成数
      inProgressTasks: data.ZBS || 0, // 在办数
      overdueTasks: data.CSS || 0, // 超时数
      completionRate: data.WCL || '0.00%', // 完成率
      raw: data,
    }
  } catch (error) {
    console.error('获取任务概览数据失败:', error)
    return null
  }
}

/**
 * 获取我的待办任务
 * @param {String} dept 部门代码
 * @param {Number} page 页码
 * @param {Number} pageSize 每页条数
 * @returns {Promise<Object>} 处理后的数据
 */
export const getMyTodoTasks = async (dept, page = 1, pageSize = 10) => {
  try {
    const data = await get('/simplequery/V_B_BHGKJSC_DDDBRW20250617190301', {
      page,
      pageSize,
      filterSql: '1=1',
      GLOBAL_STRING_MATCH_MODE: 'exact',
      queryCondition_AB_matchMode: 'exact',
      queryCondition_SUBMITTIME: '',
      orderSql: '',
      dept,
    })
    return data
  } catch (error) {
    console.error('获取我的待办任务失败:', error)
    return null
  }
}

/**
 * 获取参与待办任务
 * @param {String} dept 部门代码
 * @param {Number} page 页码
 * @param {Number} pageSize 每页条数
 * @returns {Promise<Object>} 处理后的数据
 */
export const getParticipatedTasks = async (dept, page = 1, pageSize = 10) => {
  try {
    const data = await get('/simplequery/V_B_BHGKJSC_DDDBRW20250617190302', {
      page,
      pageSize,
      filterSql: '1=1',
      GLOBAL_STRING_MATCH_MODE: 'exact',
      queryCondition_AB_matchMode: 'exact',
      queryCondition_SUBMITTIME: '',
      orderSql: '',
      dept,
    })
    return data
  } catch (error) {
    console.error('获取参与待办任务失败:', error)
    return null
  }
}

/**
 * 获取我的已办任务
 * @param {String} dept 部门代码
 * @param {Number} page 页码
 * @param {Number} pageSize 每页条数
 * @returns {Promise<Object>} 处理后的数据
 */
export const getMyCompletedTasks = async (dept, page = 1, pageSize = 10) => {
  try {
    const data = await get('/simplequery/V_B_BHGKJSC_DDDBRW20250617190303', {
      page,
      pageSize,
      filterSql: '1=1',
      GLOBAL_STRING_MATCH_MODE: 'exact',
      queryCondition_AB_matchMode: 'exact',
      queryCondition_SUBMITTIME: '',
      orderSql: '',
      dept,
    })
    return data
  } catch (error) {
    console.error('获取我的已办任务失败:', error)
    return null
  }
}

/**
 * 获取阶段在办任务
 * @param {Number} page 页码
 * @param {Number} pageSize 每页条数
 * @returns {Promise<Object>} 处理后的数据
 */
export const getPhaseInProgressTasks = async (page = 1, pageSize = 10) => {
  try {
    const data = await get('/simplequery/V_B_BHGKJSC_DDDBRW20250617191001', {
      page,
      pageSize,
      filterSql: '1=1',
      GLOBAL_STRING_MATCH_MODE: 'exact',
      queryCondition_AB_matchMode: 'exact',
      queryCondition_SUBMITTIME: '',
      orderSql: '',
    })
    return data
  } catch (error) {
    console.error('获取阶段在办任务失败:', error)
    return null
  }
}

/**
 * 获取日常在办任务
 * @param {Number} page 页码
 * @param {Number} pageSize 每页条数
 * @returns {Promise<Object>} 处理后的数据
 */
export const getDailyInProgressTasks = async (page = 1, pageSize = 10) => {
  try {
    const data = await get('/simplequery/V_B_BHGKJSC_DDDBRW20250617191002', {
      page,
      pageSize,
      filterSql: '1=1',
      GLOBAL_STRING_MATCH_MODE: 'exact',
      queryCondition_AB_matchMode: 'exact',
      queryCondition_SUBMITTIME: '',
      orderSql: '',
    })
    return data
  } catch (error) {
    console.error('获取日常在办任务失败:', error)
    return null
  }
}

/**
 * 获取阶段任务完成情况
 * @returns {Promise<Object>} 处理后的数据
 */
export const getPhaseTaskCompletion = async () => {
  try {
    const data = await get('/api/dddb/b_bhgkjsc_dddbrw/getJdrwwcqk')
    return data
  } catch (error) {
    console.error('获取阶段任务完成情况失败:', error)
    return null
  }
}

/**
 * 获取任务详情
 * @param {String} systemid 任务ID
 * @returns {Promise<Object>} 处理后的数据
 */
export const getTaskDetail = async (systemid) => {
  try {
    const data = await get(`/api/dddb/b_bhgkjsc_dddbrw/${systemid}`)
    return data
  } catch (error) {
    console.error('获取任务详情失败:', error)
    return null
  }
}

/**
 * 检查用户权限
 * @param {String} userid 用户ID
 * @returns {Promise<Object>} 处理后的数据
 */
export const checkUserPermission = async (userid) => {
  try {
    const data = await get(`/api/dddb/b_bhgkjsc_dddbrw/checkPermission/userid/${userid}`)
    return data?.result || false
  } catch (error) {
    console.error('检查用户权限失败:', error)
    return false
  }
}

/**
 * 新增工作进展
 * @param {Object} params 工作进展参数
 * @returns {Promise<Object>} 处理后的数据
 */
export const addWorkProgress = async (params) => {
  try {
    const data = await post('/api/dddb/b_bhgkjsc_dddbgzjz', params)
    return data
  } catch (error) {
    console.error('新增工作进展失败:', error)
    return null
  }
}

/**
 * 修改工作进展
 * @param {String} systemid 工作进展ID
 * @param {Object} params 工作进展参数
 * @returns {Promise<Object>} 处理后的数据
 */
export const updateWorkProgress = async (systemid, params) => {
  try {
    const data = await put(`/api/dddb/b_bhgkjsc_dddbgzjz/${systemid}`, params)
    return data
  } catch (error) {
    console.error('修改工作进展失败:', error)
    return null
  }
}

/**
 * 删除工作进展
 * @param {String} systemids 工作进展ID列表，用逗号分隔
 * @returns {Promise<Object>} 处理后的数据
 */
export const deleteWorkProgress = async (systemids) => {
  try {
    const data = await del(`/api/dddb/b_bhgkjsc_dddbgzjz/${systemids}`)
    return data
  } catch (error) {
    console.error('删除工作进展失败:', error)
    return null
  }
}

/**
 * 获取简表任务数据
 * @param {String} simpleTableId 简表ID
 * @param {Number} page 页码
 * @param {Number} pageSize 每页条数
 * @param {Object} extraParams 额外查询参数
 * @returns {Promise<Object>} 处理后的数据
 */
export const getSimpleTaskList = async (
  simpleTableId = 'V_B_BHGKJSC_DDDBRW20250619090300',
  page = 1,
  pageSize = 10,
  extraParams = {},
) => {
  try {
    const params = {
      page,
      pageSize,
      filterSql: '1=1',
      GLOBAL_STRING_MATCH_MODE: 'exact',
      queryCondition_AB_matchMode: 'exact',
      queryCondition_SUBMITTIME: '',
      orderSql: '',
      ...extraParams,
    }

    const data = await get(`/simplequery/${simpleTableId}`, params)
    return data
  } catch (error) {
    console.error('获取简表任务数据失败:', error)
    return null
  }
}

/**
 * 获取阶段任务完成情况详情
 * @param {String} dept 部门代码（QTJZ_CODE）
 * @returns {Promise<Object>} 处理后的数据
 */
export const getPhaseTaskCompletionDetail = async (dept) => {
  try {
    const data = await get('/simplequery/V_B_BHGKJSC_DDDBRW20250624205001', {
      page: 1,
      pageSize: 100,
      filterSql: '1=1',
      GLOBAL_STRING_MATCH_MODE: 'exact',
      queryCondition_AB_matchMode: 'exact',
      queryCondition_SUBMITTIME: '',
      orderSql: '',
      dept,
    })
    return data
  } catch (error) {
    console.error('获取阶段任务完成情况详情失败:', error)
    return null
  }
}

/**
 * 获取专案任务
 * @param {Number} page 页码
 * @param {Number} pageSize 每页条数
 * @returns {Promise<Object>} 处理后的数据
 */
export const getSpecialCaseTasks = async (page = 1, pageSize = 10) => {
  try {
    const data = await get('/simplequery/V_B_BHGKJSC_DDDBRW20250617191003', {
      page,
      pageSize,
      filterSql: '1=1',
      GLOBAL_STRING_MATCH_MODE: 'exact',
      queryCondition_AB_matchMode: 'exact',
      queryCondition_SUBMITTIME: '',
      orderSql: '',
    })
    return data
  } catch (error) {
    console.error('获取专案任务失败:', error)
    return null
  }
}

export default {
  addSupervisionTask,
  updateSupervisionTask,
  deleteSupervisionTask,
  getTaskOverview,
  getMyTodoTasks,
  getParticipatedTasks,
  getMyCompletedTasks,
  getPhaseInProgressTasks,
  getDailyInProgressTasks,
  getPhaseTaskCompletion,
  getTaskDetail,
  checkUserPermission,
  addWorkProgress,
  updateWorkProgress,
  deleteWorkProgress,
  getSimpleTaskList,
  getPhaseTaskCompletionDetail,
  getSpecialCaseTasks,
}
