import { post } from '@/utils/http'
import dayjs from 'dayjs'

/**
 * 态势排名API接口
 */

// 模块配置
const moduleConfig = {
  fhgbj: {
    // 妨害国边境违法犯罪
    DsRanking: '/api/dashboard/fhgbjDataThirdDsRanking', // 地市排名
    FjRanking: '/api/dashboard/fhgbjDataThirdFjRanking', // 分局排名
    QqfjRanking: '/api/dashboard/fhgbjDataThirdQqfjRanking', // 全区分局排名
    WsRanking: '/api/dashboard/fhgbjDataThirdWsRanking', // 边境五市分县局排名
    BxRanking: '/api/dashboard/fhgbjDataThirdBxRanking', // 八县排名
    ZdyRanking: '/api/dashboard/fhgbjDataThirdZdyRanking', // 自定义选择
    // 指标配置
    indicators: [
      { key: 'XSLAS', label: '刑事立案数' },
      { key: 'XZLAS', label: '行政立案数' },
      { key: 'XJRS', label: '刑事刑拘人数' },
      { key: 'CFRS', label: '行政处罚人数' },
      { key: 'ZZ', label: '刑事组织人数' },
      { key: 'YS', label: '刑事运送人数' },
      { key: 'TY', label: '刑事偷越总数' },
      { key: 'TYZGR', label: '刑事偷越(中国人)' },
      { key: 'TYWGR', label: '刑事偷越(外国人)' },
      { key: 'XSXZLAS', label: '立案总数' },
      { key: 'CCRS', label: '打击处理数' },
      { key: 'ZZYS', label: '刑事组织运送数' },
    ],
  },
  zsaj: {
    // 走私案件
    DsRanking: '/api/dashboard/zsajqkDataThirdDsRanking', // 地市排名
    FjRanking: '/api/dashboard/zsajqkDataThirdFjRanking', // 分局排名
    QqfjRanking: '/api/dashboard/zsajqkDataThirdQqfjRanking', // 全区分局排名
    WsRanking: '/api/dashboard/zsajqkDataThirdWsRanking', // 边境五市分县局排名
    BxRanking: '/api/dashboard/zsajqkDataThirdBxRanking', // 八县排名
    ZdyRanking: '/api/dashboard/zsajqkDataThirdZdyRanking', // 自定义选择
    // 指标配置
    indicators: [
      { key: 'LAS', label: '立案' },
      { key: 'ZH', label: '抓获' },
      { key: 'JL', label: '刑拘' },
      { key: 'DB', label: '逮捕' },
      { key: 'QB', label: '取保' },
      { key: 'YS', label: '移诉' },
    ],
  },
}

/**
 * 获取态势排名数据
 * @param {string} moduleType - 模块类型（fhgbj: 妨害国边境, zsaj: 走私案件）
 * @param {string} rankingType - 排名类型（DsRanking: 地市排名, FjRanking: 分局排名...）
 * @param {object} params - 查询参数
 * @returns {Promise} 排名数据
 */
export const fetchRankingData = async (moduleType, rankingType, params) => {
  try {
    // 获取模块配置
    const config = moduleConfig[moduleType]
    if (!config) {
      throw new Error(`未知模块类型: ${moduleType}`)
    }

    // 获取对应的接口URL
    const apiUrl = config[rankingType]
    if (!apiUrl) {
      throw new Error(`未知排名类型: ${rankingType}`)
    }

    // 调用接口
    const result = await post(apiUrl, params)
    return result || []
  } catch (error) {
    console.error('获取态势排名数据失败:', error)
    throw error
  }
}

/**
 * 获取分县局列表
 * @param {string} deptCode - 部门编码
 * @returns {Promise} 分县局列表
 */
export const fetchSubDepts = async (deptCode) => {
  try {
    if (!deptCode) return []
    return await post('/api/dashboard/getDw', { dept: deptCode })
  } catch (error) {
    console.error('获取分县局列表失败:', error)
    return []
  }
}

/**
 * 构建查询参数
 * @param {Array} dateRange - 日期范围
 * @param {string} type - 排序指标
 * @param {string} dept - 部门编码（可选）
 * @returns {object} 查询参数
 */
export const buildRankingParams = (dateRange, type, dept) => {
  let startTime = ''
  let endTime = ''

  if (dateRange && dateRange.length === 2) {
    startTime = dayjs(dateRange[0]).format('YYYY-MM-DD HH:mm:ss')
    endTime = dayjs(dateRange[1]).format('YYYY-MM-DD HH:mm:ss')
  } else {
    endTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    startTime = dayjs().subtract(30, 'day').format('YYYY-MM-DD HH:mm:ss')
  }

  const params = { startTime, endTime, type }

  if (dept) {
    params.dept = dept
  }

  return params
}
