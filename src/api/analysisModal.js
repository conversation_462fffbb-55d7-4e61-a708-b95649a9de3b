import { post } from '@/utils/http'
import dayjs from 'dayjs'

// 获取分县局列表
export const fetchSubDepts = async (deptCode) => {
  try {
    if (!deptCode) return []
    return await post('/api/dashboard/getDw', { dept: deptCode })
  } catch (error) {
    console.error('获取分县局列表失败:', error)
    return []
  }
}

// 构建查询参数
export const buildParams = (dateRange, dept = '45') => {
  let startTime = ''
  let endTime = ''
  if (dateRange && dateRange.length === 2) {
    startTime = dayjs(dateRange[0]).format('YYYY-MM-DD HH:mm:ss')
    endTime = dayjs(dateRange[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')
  } else {
    endTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    startTime = dayjs().subtract(30, 'day').format('YYYY-MM-DD HH:mm:ss')
  }
  return { startTime, endTime, dept }
}

// 各模块的接口和指标map
export const moduleConfig = {
  txfx: {
    // 通信信号分析
    detailUrl: '/api/dashboard/rydbDataSec', // 同环比详细数据
    trendUrl: '/api/dashboard/rydbDataTrend', // 趋势图数据
    indicatorMap: [
      { key: 'RYDB', label: '入邕抵边五市数据' },
      { key: 'XHXS', label: '信号消失五市数据' },
    ],
    // 趋势图数据指标
    trendIndicators: [
      { key: 'rydbData', name: '入邕抵边五市数据', color: '#FF4B4B' },
      { key: 'xhxsData', name: '信号消失五市数据', color: '#4B9AFF' },
    ],
  },
  fhgbj: {
    // 妨害国边境违法犯罪
    detailUrl: '/api/dashboard/fhgbjDataSec', // 同环比详细数据
    trendUrl: '/api/dashboard/fhgbjDataTrend', // 趋势图数据
    indicatorMap: [
      { key: 'XSLAS', label: '刑事立案数' },
      { key: 'XZLAS', label: '行政立案数' },
      { key: 'XJRS', label: '刑事刑拘人数' },
      { key: 'CFRS', label: '行政处罚人数' },
      { key: 'ZZ', label: '刑事组织人数' },
      { key: 'YS', label: '刑事运送人数' },
      { key: 'TY', label: '刑事偷越总数' },
      { key: 'TYZGR', label: '刑事偷越(中国人)' },
      { key: 'TYWGR', label: '刑事偷越(外国人)' },
      { key: 'XSXZLAS', label: '立案总数' },
      { key: 'CCRS', label: '打击处理数' },
      { key: 'ZZYS', label: '刑事组织运送数' },
      { key: 'XZTYZGR', label: '行政偷越中国人' },
      { key: 'XZTYWGR', label: '行政偷越外国人' },
      { key: 'XSXZTYZGR', label: '打击处理偷越（中国人）' },
      { key: 'XSXZTYWGR', label: '打击处理偷越（外国人）' },
    ],
    // 趋势图数据指标
    trendIndicators: [
      { key: 'xsxzlasData', name: '立案总数', color: '#FF7F50' },
      { key: 'xslaData', name: '刑事立案数', color: '#FF4B4B' },
      { key: 'xzlaData', name: '行政立案数', color: '#4B9AFF' },
      { key: 'ccrsData', name: '打击处理数', color: '#9370DB' },
      { key: 'xjrsData', name: '刑事刑拘人数', color: '#8F77ED' },
      { key: 'cfrsData', name: '行政处罚人数', color: '#00E4FF' },
      { key: 'xsxzTyZgrData', name: '打击处理偷越(中国人)', color: '#FF1493' },
      { key: 'xsxzTyWgrData', name: '打击处理偷越(外国人)', color: '#9932CC' },
      { key: 'tyData', name: '刑事偷越总数', color: '#ADDF84' },
      { key: 'tyzgrData', name: '刑事偷越(中国人)', color: '#F979C3' },
      { key: 'tywgrData', name: '刑事偷越(外国人)', color: '#DA70D6' },
      { key: 'xzTyzgrData', name: '行政偷越(中国人)', color: '#FF69B4' },
      { key: 'xzTywgrData', name: '行政偷越(外国人)', color: '#BA55D3' },
      { key: 'zzysData', name: '刑事组织运送数', color: '#20B2AA' },
      { key: 'zzData', name: '刑事组织人数', color: '#FFB956' },
      { key: 'ysData', name: '刑事运送人数', color: '#36CFC9' },
    ],
  },
  zsaj: {
    // 走私案件
    detailUrl: '/api/dashboard/zsajqkDataSec', // 同环比详细数据
    trendUrl: '/api/dashboard/zsajqkDataTrend', // 趋势图数据
    indicatorMap: [
      { key: 'LAS', label: '立案' },
      { key: 'ZH', label: '抓获' },
      { key: 'JL', label: '刑拘' },
      { key: 'DB', label: '逮捕' },
      { key: 'QB', label: '取保' },
      { key: 'YS', label: '移诉' },
    ],
    // 趋势图数据指标
    trendIndicators: [
      { key: 'lasData', name: '立案', color: '#36CFC9' },
      { key: 'zhData', name: '抓获', color: '#4B9AFF' },
      { key: 'jlData', name: '刑拘', color: '#FF4B4B' },
      { key: 'dbData', name: '逮捕', color: '#8F77ED' },
      { key: 'qbData', name: '取保', color: '#FFB956' },
      { key: 'ysData', name: '移诉', color: '#20B2AA' },
    ],
  },
  bjsdsq: {
    // 边境涉毒涉枪
    detailUrl: '/api/dashboard/bjsdsqfzDataSec', // 同环比详细数据
    trendUrl: '/api/dashboard/bjsdsqfzDataTrend', // 趋势图数据
    indicatorMap: [
      { key: 'SDXSLAS', label: '涉毒刑事立案数' },
      { key: 'ZHRYS', label: '抓获人员数' },
      { key: 'XDXZAJS', label: '涉毒行政案件数' },
      { key: 'CCXDRYS', label: '查处吸毒人数' },
      { key: 'SQLAS', label: '涉枪立案数' },
      { key: 'SQZHRS', label: '涉枪抓获人数' },
      { key: 'JHQZS', label: '缴获枪支数' },
    ],
    // 趋势图数据指标
    trendIndicators: [
      { key: 'sdxslasData', name: '涉毒刑事立案', color: '#FF4B4B' },
      { key: 'zhrsData', name: '抓获人员总数', color: '#8F77ED' },
      { key: 'sdxzlasData', name: '涉毒行政案件', color: '#4B9AFF' },
      { key: 'xdrsData', name: '查处吸毒人数', color: '#36CFC9' },
      { key: 'sqladData', name: '涉枪立案数', color: '#FFB956' },
      { key: 'sqzhrsData', name: '涉枪抓获人数', color: '#ADDF84' },
      { key: 'jhqzsData', name: '缴获枪支数', color: '#F979C3' },
    ],
  },
  kabfjc: {
    // 口岸边防检查
    detailUrl: '/api/dashboard/kabfjcqkDataSec', // 同环比详细数据
    trendUrl: '/api/dashboard/kabfjcqkDataTrend', // 趋势图数据
    indicatorMap: [
      { key: 'CJ_RY', label: '出境人员' },
      { key: 'RJ_RY', label: '入境人员' },
    ],
    // 趋势图数据指标
    trendIndicators: [
      { key: 'cjRyData', name: '出境人员', color: '#FF4B4B' },
      { key: 'rjRyData', name: '入境人员', color: '#4B9AFF' },
    ],
    // 是否需要计算总计
    needTotal: true,
    totalName: '出入境总数',
    totalColor: '#00FFCC',
  },
  bjjc: {
    // 边境检查站缉查
    detailUrl: '/api/dashboard/bjjczcjqkDataSec', // 同环比详细数据
    trendUrl: '/api/dashboard/bjjczcjqkDataTrend', // 趋势图数据
    indicatorMap: [
      { key: 'cl', label: '车辆进入' },
      { key: 'ry', label: '人员进入' },
    ],
    // 趋势图数据指标
    trendIndicators: [
      { key: 'clJrData', name: '车辆进入', color: '#FF4B4B' },
      { key: 'ryJrData', name: '人员进入', color: '#4B9AFF' },
    ],
    // 是否需要计算总计
    needTotal: true,
    totalName: '总进入量',
    totalColor: '#00FFCC',
  },
  crjzj: {
    // 出入境证件办理
    detailUrl: '/api/dashboard/crjzjblqkDataSec', // 同环比详细数据
    trendUrl: '/api/dashboard/crjzjblqkDataTrend', // 趋势图数据
    indicatorMap: [
      { key: 'zgrbzs', label: '中国人办证' },
      { key: 'wgrbzs', label: '外国人办证' },
    ],
    // 趋势图数据指标
    trendIndicators: [
      { key: 'zgrbzs', name: '中国人办证', color: '#FF4B4B' },
      { key: 'wgrbzs', name: '外国人办证', color: '#4B9AFF' },
    ],
    // 是否需要计算总计
    needTotal: true,
    totalName: '总办证量',
    totalColor: '#00FFCC',
  },
}

// 通用接口请求
export const fetchModuleData = async (moduleKey) => {
  const config = moduleConfig[moduleKey]
  if (!config) throw new Error('未知模块')
  const params = buildParams()
  const res = await post(config.url, params)
  return res
}

// 获取同环比详细数据
export const fetchDetailData = async (module, params, detailType = null) => {
  try {
    const config = moduleConfig[module]
    if (!config) {
      throw new Error(`未找到模块配置: ${module}`)
    }

    // 如果是详情查询，使用不同的接口
    if (detailType) {
      const response = await post('/api/dashboard/fhgbjDataDetail', {
        dept: params.dept || '45',
        startTime: params.startTime,
        endTime: params.endTime,
        type: detailType,
        page: params.page || '1',
        pageSize: params.pageSize || '10',
      })
      return response
    }

    // 原有的同环比数据查询
    const response = await post(config.detailUrl, params)
    return response
  } catch (error) {
    console.error('获取详细数据失败:', error)
    throw error
  }
}

// 获取趋势图数据
export const fetchTrendData = async (moduleType, params) => {
  try {
    const config = moduleConfig[moduleType]
    if (!config) throw new Error(`未知模块: ${moduleType}`)

    return await post(config.trendUrl, params)
  } catch (error) {
    console.error(`获取${moduleType}趋势图数据失败:`, error)
    return null
  }
}

// 处理折线图同期数据的函数
export const processLineChartData = (moduleType, trendData, selectedIndicatorKey) => {
  if (!trendData || !selectedIndicatorKey) return { xAxis: [], series: [] }

  const config = moduleConfig[moduleType]
  if (!config) return { xAxis: [], series: [] }

  // 提取月份标签
  const extractMonths = (dataArray) => {
    if (!dataArray || !Array.isArray(dataArray)) return []
    return dataArray.map((item) => {
      const month = new Date(item.DAYTIME).getMonth() + 1
      return `${month}月`
    })
  }

  // 获取x轴数据
  let xAxisData = []
  if (trendData[selectedIndicatorKey] && Array.isArray(trendData[selectedIndicatorKey])) {
    xAxisData = extractMonths(trendData[selectedIndicatorKey])
  }

  // 如果没有找到月份数据，使用默认月份
  if (xAxisData.length === 0) {
    xAxisData = [
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ]
  }

  // 获取选中指标的配置
  const indicatorConfig = config.trendIndicators.find((item) => item.key === selectedIndicatorKey)
  if (!indicatorConfig) return { xAxis: [], series: [] }

  // 同期数据的键名
  const qnIndicatorKey = `${selectedIndicatorKey}Qn`

  // 处理本期和同期数据
  const currentPeriodData = trendData[selectedIndicatorKey]?.map((item) => item.TOTAL || 0) || []
  const lastYearData = trendData[qnIndicatorKey]?.map((item) => item.TOTAL || 0) || []

  // 返回处理后的数据
  return {
    xAxis: xAxisData,
    series: [
      {
        name: '本期',
        data: currentPeriodData,
        color: '#FF4B4B', // 本期使用红色
      },
      {
        name: '同期',
        data: lastYearData,
        color: '#00FF00', // 同期使用绿色
      },
    ],
  }
}

// 处理趋势图数据
export const processChartData = (moduleType, trendData) => {
  if (!trendData) return { xAxis: [], series: [] }

  const config = moduleConfig[moduleType]
  if (!config) return { xAxis: [], series: [] }

  // 提取月份标签
  const extractMonths = (dataArray) => {
    if (!dataArray || !Array.isArray(dataArray)) return []
    return dataArray.map((item) => {
      const month = new Date(item.DAYTIME).getMonth() + 1
      return `${month}月`
    })
  }

  // 获取x轴数据
  let xAxisData = []
  if (config.trendIndicators && config.trendIndicators.length > 0) {
    // 尝试从各个指标中获取月份数据
    for (const indicator of config.trendIndicators) {
      if (trendData[indicator.key] && Array.isArray(trendData[indicator.key])) {
        xAxisData = extractMonths(trendData[indicator.key])
        if (xAxisData.length > 0) break
      }
    }
  }

  // 如果没有找到月份数据，使用默认月份
  if (xAxisData.length === 0) {
    xAxisData = [
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ]
  }

  // 处理图表数据
  let seriesData = []
  if (config.trendIndicators && config.trendIndicators.length > 0) {
    seriesData = config.trendIndicators
      .filter((indicator) => trendData[indicator.key] && Array.isArray(trendData[indicator.key]))
      .map((indicator) => ({
        name: indicator.name,
        data: trendData[indicator.key].map((item) => item.TOTAL || 0),
        color: indicator.color,
      }))
  }

  // 如果需要计算总计
  if (config.needTotal && seriesData.length >= 2) {
    // 计算总数
    const totalData = seriesData[0].data.map((val, i) => {
      let sum = val
      for (let j = 1; j < seriesData.length; j++) {
        sum += seriesData[j].data[i] || 0
      }
      return sum
    })

    seriesData.push({
      name: config.totalName,
      data: totalData,
      color: config.totalColor,
    })
  }

  return {
    xAxis: xAxisData,
    series: seriesData,
  }
}

// 处理概览数据
export const processOverviewData = (moduleType, detailData) => {
  if (!detailData)
    return {
      currentYear: { title: '本期', data: [] },
      lastYear: { title: '同期', data: [] },
      beforeLastYear: { title: '上期', data: [] },
    }

  const config = moduleConfig[moduleType]
  if (!config)
    return {
      currentYear: { title: '本期', data: [] },
      lastYear: { title: '同期', data: [] },
      beforeLastYear: { title: '上期', data: [] },
    }

  // 准备返回数据
  const result = {
    currentYear: { title: '本期', data: [] },
    lastYear: { title: '同期', data: [] },
    beforeLastYear: { title: '上期', data: [] },
  }

  // 处理同比环比数据，添加+/-符号
  const formatCompareValue = (value, status) => {
    if (!value) return '0%'
    // 判断数值状态: 1表示上升(+)，0表示下降(-)
    const prefix = status === 1 ? '+' : status === 0 ? '-' : ''
    // 确保值中不包含已有的+/-符号
    let cleanValue = value.toString().replace(/^[+-]/, '')
    // 如果值是0%，则不添加符号
    if (cleanValue === '0%' || cleanValue === '0.00%') return '0%'
    return `${prefix}${cleanValue}`
  }

  // 获取trendIndicators中的标准名称
  const indicatorNameMap = {}
  // 直接遍历trendIndicators和indicatorMap创建映射关系
  config.trendIndicators.forEach((ind) => {
    // 从key中移除Data后缀
    const baseKey = ind.key.replace('Data', '')
    // 创建多种可能的映射键
    indicatorNameMap[baseKey.toUpperCase()] = ind.name
    // 某些特殊情况处理(如XSLA->XSLAS的映射)
    if (baseKey === 'xsla') indicatorNameMap['XSLAS'] = ind.name
    if (baseKey === 'xzla') indicatorNameMap['XZLAS'] = ind.name
    if (baseKey === 'ty') indicatorNameMap['TYZS'] = ind.name
  })

  // 添加调试日志，检查映射关系
  console.log('指标名称映射:', indicatorNameMap)

  // 根据不同模块处理概览数据
  switch (moduleType) {
    case 'txfx': {
      // 通信信号分析
      const indicators = [
        {
          key: 'RYDB',
          label: '入邕抵边五市数据',
          current: detailData.RYDB || 0,
          previousSame: detailData.RYDB_QN || 0,
          previousPeriod: detailData.RYDB_SY || 0,
          tbStatus: detailData.RYDBTBZT || 0,
          ybStatus: detailData.RYDBYBZT || 0,
          tbValue: detailData.RYDBTB || '0.00%',
          ybValue: detailData.RYDBYB || '0.00%',
        },
        {
          key: 'XHXS',
          label: '信号消失五市数据',
          current: detailData.XHXS || 0,
          previousSame: detailData.XHXS_QN || 0,
          previousPeriod: detailData.XHXS_SY || 0,
          tbStatus: detailData.XHXSTBZT || 0,
          ybStatus: detailData.XHXSYBZT || 0,
          tbValue: detailData.XHXSTB || '0.00%',
          ybValue: detailData.XHXSYB || '0.00%',
        },
      ]

      // 设置概览数据 - 本期（包含同环比）
      result.currentYear.data = indicators.map((item) => ({
        label: item.label,
        value: item.current,
        same: formatCompareValue(item.tbValue, item.tbStatus),
        chain: formatCompareValue(item.ybValue, item.ybStatus),
      }))

      // 设置概览数据 - 同期（不含同环比）
      result.lastYear.data = indicators.map((item) => ({
        label: item.label,
        value: item.previousSame,
        same: '',
        chain: '',
      }))

      // 设置概览数据 - 上期（不含同环比）
      result.beforeLastYear.data = indicators.map((item) => ({
        label: item.label,
        value: item.previousPeriod,
        same: '',
        chain: '',
      }))

      break
    }
    case 'fhgbj': {
      // 修改indicators数组，使用标准名称
      const indicators = [
        {
          key: 'XJRS',
          // 使用映射中的标准名称，如果没有则使用原来的
          label: indicatorNameMap['XJRS'] || '刑事刑拘人数',
          current: detailData.XJRS || 0,
          previousSame: detailData.XJRS_QN || 0,
          previousPeriod: detailData.XJRS_SY || 0,
          tbStatus: detailData.XJRSTBZT || 0,
          ybStatus: detailData.XJRSYBZT || 0,
          tbValue: detailData.XJRSTB || '0.00%',
          ybValue: detailData.XJRSYB || '0.00%',
        },
        {
          key: 'XSLAS',
          label: indicatorNameMap['XSLAS'] || '刑事立案数',
          current: detailData.XSLAS || 0,
          previousSame: detailData.XSLAS_QN || 0,
          previousPeriod: detailData.XSLAS_SY || 0,
          tbStatus: detailData.XSLASTBZT || 0,
          ybStatus: detailData.XSLASYBZT || 0,
          tbValue: detailData.XSLASTB || '0.00%',
          ybValue: detailData.XSLASYB || '0.00%',
        },
        {
          key: 'XZLAS',
          label: indicatorNameMap['XZLAS'] || '行政立案数',
          current: detailData.XZLAS || 0,
          previousSame: detailData.XZLAS_QN || 0,
          previousPeriod: detailData.XZLAS_SY || 0,
          tbStatus: detailData.XZLASTBZT || 0,
          ybStatus: detailData.XZLASYBZT || 0,
          tbValue: detailData.XZLASTB || '0.00%',
          ybValue: detailData.XZLASYB || '0.00%',
        },
        {
          key: 'CFRS',
          label: indicatorNameMap['CFRS'] || '行政处罚人数',
          current: detailData.CFRS || 0,
          previousSame: detailData.CFRS_QN || 0,
          previousPeriod: detailData.CFRS_SY || 0,
          tbStatus: detailData.CFRSTBZT || 0,
          ybStatus: detailData.CFRSYBZT || 0,
          tbValue: detailData.CFRSTB || '0.00%',
          ybValue: detailData.CFRSYB || '0.00%',
        },
        {
          key: 'ZZ',
          label: indicatorNameMap['ZZ'] || '刑事组织人数',
          current: detailData.ZZ || 0,
          previousSame: detailData.ZZ_QN || 0,
          previousPeriod: detailData.ZZ_SY || 0,
          tbStatus: detailData.ZZTBZT || 0,
          ybStatus: detailData.ZZYBZT || 0,
          tbValue: detailData.ZZTB || '0.00%',
          ybValue: detailData.ZZYB || '0.00%',
        },
        {
          key: 'YS',
          label: indicatorNameMap['YS'] || '刑事运送人数',
          current: detailData.YS || 0,
          previousSame: detailData.YS_QN || 0,
          previousPeriod: detailData.YS_SY || 0,
          tbStatus: detailData.YSTBZT || 0,
          ybStatus: detailData.YSYBZT || 0,
          tbValue: detailData.YSTB || '0.00%',
          ybValue: detailData.YSYB || '0.00%',
        },
        {
          key: 'TY',
          label: indicatorNameMap['TY'] || '刑事偷越总数',
          current: detailData.TY || 0,
          previousSame: detailData.TY_QN || 0,
          previousPeriod: detailData.TY_SY || 0,
          tbStatus: detailData.TYTBZT || 0,
          ybStatus: detailData.TYYBZT || 0,
          tbValue: detailData.TYTB || '0.00%',
          ybValue: detailData.TYYB || '0.00%',
        },
        {
          key: 'TYZGR',
          label: indicatorNameMap['TYZGR'] || '刑事偷越(中国人)',
          current: detailData.TYZGR || 0,
          previousSame: detailData.TYZGR_QN || 0,
          previousPeriod: detailData.TYZGR_SY || 0,
          tbStatus: detailData.TYZGRTBZT || 0,
          ybStatus: detailData.TYZGRYBZT || 0,
          tbValue: detailData.TYZGRTB || '0.00%',
          ybValue: detailData.TYZGRYB || '0.00%',
        },
        {
          key: 'XSXZLAS',
          label: indicatorNameMap['XSXZLAS'] || '立案总数',
          current: detailData.XSXZLAS || 0,
          previousSame: detailData.XSXZLAS_QN || 0,
          previousPeriod: detailData.XSXZLAS_SY || 0,
          tbStatus: detailData.XSXZLASTBZT || 0,
          ybStatus: detailData.XSXZLASYBZT || 0,
          tbValue: detailData.XSXZLASTB || '0.00%',
          ybValue: detailData.XSXZLASYB || '0.00%',
        },
        {
          key: 'CCRS',
          label: indicatorNameMap['CCRS'] || '打击处理数',
          current: detailData.CCRS || 0,
          previousSame: detailData.CCRS_QN || 0,
          previousPeriod: detailData.CCRS_SY || 0,
          tbStatus: detailData.CCRSTBZT || 0,
          ybStatus: detailData.CCRSYBZT || 0,
          tbValue: detailData.CCRSTB || '0.00%',
          ybValue: detailData.CCRSYB || '0.00%',
        },
        {
          key: 'ZZYS',
          label: indicatorNameMap['ZZYS'] || '刑事组织运送数',
          current: detailData.ZZYS || 0,
          previousSame: detailData.ZZYS_QN || 0,
          previousPeriod: detailData.ZZYS_SY || 0,
          tbStatus: detailData.ZZYSTBZT || 0,
          ybStatus: detailData.ZZYSYBZT || 0,
          tbValue: detailData.ZZYSTB || '0.00%',
          ybValue: detailData.ZZYSYB || '0.00%',
        },
        {
          key: 'TYZS',
          label: indicatorNameMap['TYZS'] || '刑事偷越总数',
          current: detailData.TYZS || 0,
          previousSame: detailData.TYZS_QN || 0,
          previousPeriod: detailData.TYZS_SY || 0,
          tbStatus: detailData.TYZSTBZT || 0,
          ybStatus: detailData.TYZSYBZT || 0,
          tbValue: detailData.TYZSTB || '0.00%',
          ybValue: detailData.TYZSYB || '0.00%',
        },
        {
          key: 'TYWGR',
          label: indicatorNameMap['TYWGR'] || '刑事偷越(外国人)',
          current: detailData.TYWGR || 0,
          previousSame: detailData.TYWGR_QN || 0,
          previousPeriod: detailData.TYWGR_SY || 0,
          tbStatus: detailData.TYWGRTBZT || 0,
          ybStatus: detailData.TYWGRYBZT || 0,
          tbValue: detailData.TYWGRTB || '0.00%',
          ybValue: detailData.TYWGRYB || '0.00%',
        },
        {
          key: 'XZTYZGR',
          label: indicatorNameMap['XZTYZGR'] || '行政偷越中国人',
          current: detailData.XZTYZGR || 0,
          previousSame: detailData.XZTYZGR_QN || 0,
          previousPeriod: detailData.XZTYZGR_SY || 0,
          tbStatus: detailData.XZTYZGRTBZT || 0,
          ybStatus: detailData.XZTYZGRYBZT || 0,
          tbValue: detailData.XZTYZGRTB || '0.00%',
          ybValue: detailData.XZTYZGRYB || '0.00%',
        },
        {
          key: 'XZTYWGR',
          label: indicatorNameMap['XZTYWGR'] || '行政偷越外国人',
          current: detailData.XZTYWGR || 0,
          previousSame: detailData.XZTYWGR_QN || 0,
          previousPeriod: detailData.XZTYWGR_SY || 0,
          tbStatus: detailData.XZTYWGRTBZT || 0,
          ybStatus: detailData.XZTYWGRYBZT || 0,
          tbValue: detailData.XZTYWGRTB || '0.00%',
          ybValue: detailData.XZTYWGRYB || '0.00%',
        },
        {
          key: 'XSXZTYZGR',
          label: indicatorNameMap['XSXZTYZGR'] || '打击处理偷越（中国人）',
          current: detailData.XSXZTYZGR || 0,
          previousSame: detailData.XSXZTYZGR_QN || 0,
          previousPeriod: detailData.XSXZTYZGR_SY || 0,
          tbStatus: detailData.XSXZTYZGRTBZT || 0,
          ybStatus: detailData.XSXZTYZGRYBZT || 0,
          tbValue: detailData.XSXZTYZGRTB || '0.00%',
          ybValue: detailData.XSXZTYZGRYB || '0.00%',
        },
        {
          key: 'XSXZTYWGR',
          label: indicatorNameMap['XSXZTYWGR'] || '打击处理偷越（外国人）',
          current: detailData.XSXZTYWGR || 0,
          previousSame: detailData.XSXZTYWGR_QN || 0,
          previousPeriod: detailData.XSXZTYWGR_SY || 0,
          tbStatus: detailData.XSXZTYWGRTBZT || 0,
          ybStatus: detailData.XSXZTYWGRYBZT || 0,
          tbValue: detailData.XSXZTYWGRTB || '0.00%',
          ybValue: detailData.XSXZTYWGRYB || '0.00%',
        },
        {
          key: 'XSXZLAS',
          label: indicatorNameMap['XSXZLAS'] || '立案总数',
          current: detailData.XSXZLAS || 0,
          previousSame: detailData.XSXZLAS_QN || 0,
          previousPeriod: detailData.XSXZLAS_SY || 0,
          tbStatus: detailData.XSXZLASTBZT || 0,
          ybStatus: detailData.XSXZLASYBZT || 0,
          tbValue: detailData.XSXZLASTB || '0.00%',
          ybValue: detailData.XSXZLASYB || '0.00%',
        },
      ]

      // 设置概览数据 - 本期（包含同环比）
      result.currentYear.data = indicators.map((item) => ({
        label: item.label,
        value: item.current,
        same: formatCompareValue(item.tbValue, item.tbStatus),
        chain: formatCompareValue(item.ybValue, item.ybStatus),
      }))

      // 设置概览数据 - 同期（不含同环比）
      result.lastYear.data = indicators.map((item) => ({
        label: item.label,
        value: item.previousSame,
        same: '',
        chain: '',
      }))

      // 设置概览数据 - 上期（不含同环比）
      result.beforeLastYear.data = indicators.map((item) => ({
        label: item.label,
        value: item.previousPeriod,
        same: '',
        chain: '',
      }))

      break
    }

    case 'zsaj': {
      // 所有指标数据
      const indicators = [
        {
          key: 'LAS',
          label: '立案',
          current: detailData.LAS || 0,
          previousSame: detailData.LAS_QN || 0,
          previousPeriod: detailData.LAS_SY || 0,
          tbStatus: detailData.LASTBZT || 0,
          ybStatus: detailData.LASYBZT || 0,
          tbValue: detailData.LASTB || '0.00%',
          ybValue: detailData.LASYB || '0.00%',
        },
        {
          key: 'ZH',
          label: '抓获',
          current: detailData.ZH || 0,
          previousSame: detailData.ZH_QN || 0,
          previousPeriod: detailData.ZH_SY || 0,
          tbStatus: detailData.ZHTBZT || 0,
          ybStatus: detailData.ZHYBZT || 0,
          tbValue: detailData.ZHTB || '0.00%',
          ybValue: detailData.ZHYB || '0.00%',
        },
        {
          key: 'JL',
          label: '刑拘',
          current: detailData.JL || 0,
          previousSame: detailData.JL_QN || 0,
          previousPeriod: detailData.JL_SY || 0,
          tbStatus: detailData.JLTBZT || 0,
          ybStatus: detailData.JLYBZT || 0,
          tbValue: detailData.JLTB || '0.00%',
          ybValue: detailData.JLYB || '0.00%',
        },
        {
          key: 'DB',
          label: '逮捕',
          current: detailData.DB || 0,
          previousSame: detailData.DB_QN || 0,
          previousPeriod: detailData.DB_SY || 0,
          tbStatus: detailData.DBTBZT || 0,
          ybStatus: detailData.DBYBZT || 0,
          tbValue: detailData.DBTB || '0.00%',
          ybValue: detailData.DBYB || '0.00%',
        },
        {
          key: 'QB',
          label: '取保',
          current: detailData.QB || 0,
          previousSame: detailData.QB_QN || 0,
          previousPeriod: detailData.QB_SY || 0,
          tbStatus: detailData.QBTBZT || 0,
          ybStatus: detailData.QBYBZT || 0,
          tbValue: detailData.QBTB || '0.00%',
          ybValue: detailData.QBYB || '0.00%',
        },
        {
          key: 'YS',
          label: '移诉',
          current: detailData.YS || 0,
          previousSame: detailData.YS_QN || 0,
          previousPeriod: detailData.YS_SY || 0,
          tbStatus: detailData.YSTBZT || 0,
          ybStatus: detailData.YSYBZT || 0,
          tbValue: detailData.YSTB || '0.00%',
          ybValue: detailData.YSYB || '0.00%',
        },
      ]

      // 设置概览数据 - 本期（包含同环比）
      result.currentYear.data = indicators.map((item) => ({
        label: item.label,
        value: item.current,
        same: formatCompareValue(item.tbValue, item.tbStatus),
        chain: formatCompareValue(item.ybValue, item.ybStatus),
      }))

      // 设置概览数据 - 同期（不含同环比）
      result.lastYear.data = indicators.map((item) => ({
        label: item.label,
        value: item.previousSame,
        same: '',
        chain: '',
      }))

      // 设置概览数据 - 上期（不含同环比）
      result.beforeLastYear.data = indicators.map((item) => ({
        label: item.label,
        value: item.previousPeriod,
        same: '',
        chain: '',
      }))

      break
    }

    case 'bjsdsq': {
      // 所有指标数据
      const indicators = [
        {
          key: 'SDXSLAS',
          label: '涉毒刑事立案',
          current: detailData.SDXSLAS || 0,
          previousSame: detailData.SDXSLAS_QN || 0,
          previousPeriod: detailData.SDXSLAS_SY || 0,
          tbStatus: detailData.SDXSLASTBZT || 0,
          ybStatus: detailData.SDXSLASYBZT || 0,
          tbValue: detailData.SDXSLASTB || '0.00%',
          ybValue: detailData.SDXSLASYB || '0.00%',
        },
        {
          key: 'ZHRYS',
          label: '抓获人员总数',
          current: detailData.ZHRYS || 0,
          previousSame: detailData.ZHRYS_QN || 0,
          previousPeriod: detailData.ZHRYS_SY || 0,
          tbStatus: detailData.ZHRYSTBZT || 0,
          ybStatus: detailData.ZHRYSYBZT || 0,
          tbValue: detailData.ZHRYSTB || '0.00%',
          ybValue: detailData.ZHRYSYB || '0.00%',
        },
        {
          key: 'XDXZAJS',
          label: '涉毒行政案件',
          current: detailData.XDXZAJS || 0,
          previousSame: detailData.XDXZAJS_QN || 0,
          previousPeriod: detailData.XDXZAJS_SY || 0,
          tbStatus: detailData.XDXZAJSTBZT || 0,
          ybStatus: detailData.XDXZAJSYBZT || 0,
          tbValue: detailData.XDXZAJSTB || '0.00%',
          ybValue: detailData.XDXZAJSYB || '0.00%',
        },
        {
          key: 'CCXDRYS',
          label: '查处吸毒人数',
          current: detailData.CCXDRYS || 0,
          previousSame: detailData.CCXDRYS_QN || 0,
          previousPeriod: detailData.CCXDRYS_SY || 0,
          tbStatus: detailData.CCXDRYSTBZT || 0,
          ybStatus: detailData.CCXDRYSYBZT || 0,
          tbValue: detailData.CCXDRYSTB || '0.00%',
          ybValue: detailData.CCXDRYSYB || '0.00%',
        },
        {
          key: 'SQLAS',
          label: '涉枪立案数',
          current: detailData.SQLAS || 0,
          previousSame: detailData.SQLAS_QN || 0,
          previousPeriod: detailData.SQLAS_SY || 0,
          tbStatus: detailData.SQLASTBZT || 0,
          ybStatus: detailData.SQLASYBZT || 0,
          tbValue: detailData.SQLASTB || '0.00%',
          ybValue: detailData.SQLASYB || '0.00%',
        },
        {
          key: 'SQZHRS',
          label: '涉枪抓获人数',
          current: detailData.SQZHRS || 0,
          previousSame: detailData.SQZHRS_QN || 0,
          previousPeriod: detailData.SQZHRS_SY || 0,
          tbStatus: detailData.SQZHRSTBZT || 0,
          ybStatus: detailData.SQZHRSYBZT || 0,
          tbValue: detailData.SQZHRSTB || '0.00%',
          ybValue: detailData.SQZHRSYB || '0.00%',
        },
        {
          key: 'JHQZS',
          label: '缴获枪支数',
          current: detailData.JHQZS || 0,
          previousSame: detailData.JHQZS_QN || 0,
          previousPeriod: detailData.JHQZS_SY || 0,
          tbStatus: detailData.JHQZSTBZT || 0,
          ybStatus: detailData.JHQZSYBZT || 0,
          tbValue: detailData.JHQZSTB || '0.00%',
          ybValue: detailData.JHQZSYB || '0.00%',
        },
      ]

      // 设置概览数据 - 本期（包含同环比）
      result.currentYear.data = indicators.map((item) => ({
        label: item.label,
        value: item.current,
        same: formatCompareValue(item.tbValue, item.tbStatus),
        chain: formatCompareValue(item.ybValue, item.ybStatus),
      }))

      // 设置概览数据 - 同期（不含同环比）
      result.lastYear.data = indicators.map((item) => ({
        label: item.label,
        value: item.previousSame,
        same: '',
        chain: '',
      }))

      // 设置概览数据 - 上期（不含同环比）
      result.beforeLastYear.data = indicators.map((item) => ({
        label: item.label,
        value: item.previousPeriod,
        same: '',
        chain: '',
      }))

      break
    }

    // 其他模块使用通用处理逻辑
    default: {
      // 通用处理逻辑，适用于结构相对简单的模块
      // 检查配置是否存在
      if (!config.indicatorMap || !Array.isArray(config.indicatorMap)) break

      const data = []

      // 处理每个指标
      config.indicatorMap.forEach((ind) => {
        const item = {}
        item.label = ind.label
        item.value = detailData[ind.key] || 0

        // 同比和环比（如果存在）
        const tbKey = `${ind.key}_TB` // 同比率字段
        const ybKey = `${ind.key}_YB` // 环比率字段
        const tbStatusKey = `${ind.key}_TBZT` // 同比状态字段
        const ybStatusKey = `${ind.key}_YBZT` // 环比状态字段

        // 使用状态字段添加+/-符号
        item.same = formatCompareValue(detailData[tbKey], detailData[tbStatusKey])
        item.chain = formatCompareValue(detailData[ybKey], detailData[ybStatusKey])

        data.push(item)
      })

      // 如果需要计算总计
      if (config.needTotal && data.length >= 2) {
        const totalItem = {
          label: config.totalName || '总计',
          value: data.reduce((sum, item) => sum + item.value, 0),
          same: '0%',
          chain: '0%',
        }
        data.push(totalItem)
      }

      // 设置本期数据
      result.currentYear.data = data

      // 简化处理同期和上期数据（仅显示值，不显示同环比）
      result.lastYear.data = data.map((item) => ({
        label: item.label,
        value: Math.floor(item.value * 0.9), // 简化处理，假定同期数据为当前的90%
        same: '',
        chain: '',
      }))

      result.beforeLastYear.data = data.map((item) => ({
        label: item.label,
        value: Math.floor(item.value * 0.8), // 简化处理，假定上期数据为当前的80%
        same: '',
        chain: '',
      }))
      break
    }
  }

  return result
}
