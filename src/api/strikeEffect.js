import { post } from '@/utils/http'
import { useQueryConditionStore } from '@/stores/queryCondition'
import dayjs from 'dayjs'

/**
 * 打击成效相关接口
 * 提供与后端打击成效相关的接口交互功能
 */

// 处理查询参数，添加时间范围和部门信息
const buildParams = (dept = '45', extraParams = {}) => {
  const queryStore = useQueryConditionStore()

  // 获取日期范围参数
  let startTime = ''
  let endTime = ''

  if (queryStore.dateRange && queryStore.dateRange.length === 2) {
    startTime = dayjs(queryStore.dateRange[0]).format('YYYY-MM-DD HH:mm:ss')
    endTime = dayjs(queryStore.dateRange[1]).format('YYYY-MM-DD HH:mm:ss')
  } else {
    // 默认查询最近30天
    endTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    startTime = dayjs().subtract(30, 'day').format('YYYY-MM-DD HH:mm:ss')
  }

  return {
    startTime,
    endTime,
    dept: queryStore.dept || dept,
    ...extraParams,
  }
}

/**
 * 获取边境毒品成效数据
 * @param {String} dept 部门代码，默认为全省(45)
 * @returns {Promise<Object>} 处理后的数据
 */
export const getBorderDrugEffectData = async (dept = '45') => {
  try {
    const params = buildParams(dept)
    const data = await post('/api/djcxDashboard/bjdpcxData', params)
    const less = data.dataDpjgLess || []
    const more = data.dataDpjgMore || []
    console.log('less', less)
    console.log('more', more)

    // 横坐标（季度）
    const xData = less.map((item) => item.JD)
    // 纵坐标
    const lessData = less.map((item) => item.SL)
    const moreData = more.map((item) => item.SL)
    console.log('api lessData', lessData)
    console.log('api moreData', moreData)
    console.log('api xData', xData)
    // 返回处理后的数据，包括各种毒品类型和贩运方式
    return {
      // 贩运方式数据
      transportMethods: {
        airway: {
          value: data.KY || 0,
          percentage: data.KYZB || '0.00%',
        },
        railway: {
          value: data.TL || 0,
          percentage: data.TLZB || '0.00%',
        },
        landway: {
          value: data.LL || 0,
          percentage: data.LLZB || '0.00%',
        },
        mailway: {
          value: data.YL || 0,
          percentage: data.YLZB || '0.00%',
        },
        waterway: {
          value: data.SL || 0,
          percentage: data.SLZB || '0.00%',
        },
        others: {
          value: data.QTFY || 0,
          percentage: data.QTFYZB || '0.00%',
        },
      },
      // 毒品类型数据
      drugTypes: {
        total: {
          value: data.DPZS || 0,
        },
        heroin: {
          // 海洛因
          value: data.HLY || 0,
          percentage: data.HLYZB || '0.00%',
        },
        methamphetamine: {
          // 冰毒
          value: data.BD || 0,
          percentage: data.BDZB || '0.00%',
        },
        etomidate: {
          // 依托米酯
          value: data.YTMZ || 0,
          percentage: data.YTMZZB || '0.00%',
        },
        ketamine: {
          // 氯胺酮
          value: data.LAT || 0,
          percentage: data.LATZB || '0.00%',
        },
        fluoketamine: {
          // 氟胺酮
          value: data.FAT || 0,
          percentage: data.FATZB || '0.00%',
        },
        cannabis: {
          // 大麻
          value: data.DM || 0,
          percentage: data.DMZB || '0.00%',
        },
        nimesulide: {
          // 尼美舒利片
          value: data.NMXP || 0,
          percentage: data.NMXPZB || '0.00%',
        },
        syntheticCannabinoids: {
          // 合成大麻素
          value: data.HCDMS || 0,
          percentage: data.HCDMSZB || '0.00%',
        },
        others: {
          // 其他毒品
          value: data.QTDP || 0,
          percentage: data.QTDPZB || '0.00%',
        },
      },
      // 犯罪人员相关数据
      criminalPersonnel: {
        drugRelatedHistory: data.BLQK || 0,
        otherHistory: data.QTQK || 0,
        totalSuspects: data.ZSARY || 0,
        borderSuspects: data.BJSDRY || 0,
      },
      // 同比柱状图数据
      compareBarChartData: {
        xData,
        lessData,
        moreData,
      },

      // 原始数据
      raw: data,
    }
  } catch (error) {
    console.error('获取边境毒品成效数据失败:', error)
    return null
  }
}

/**
 * 获取边境毒品成效地域数据
 * @param {String} dept 部门代码，默认为全省(45)
 * @returns {Promise<Object>} 处理后的数据
 */
export const getBorderDrugRegionData = async (dept = '45') => {
  try {
    const params = buildParams(dept)
    const data = await post('/api/djcxDashboard/bjdpcxDyData', params)

    // 返回处理后的数据
    return {
      // 抓获地域分布
      captureRegions:
        data.zhdyfb?.map((item) => ({
          region: item.DS,
          code: item.QXD || '',
          value: item.SL || 0,
          percentage: item.ZB || '0.00%',
        })) || [],

      // 来源地分布
      sourceRegions:
        data.lydfb?.map((item) => ({
          region: item.DS,
          code: item.LYD || '',
          value: item.SL || 0,
          percentage: item.ZB || '0.00%',
        })) || [],

      // 贩运线路分布
      transportRegions:
        data.fyxlfb?.map((item) => ({
          region: item.DS,
          code: item.TJDQ || '',
          value: item.SL || 0,
          percentage: item.ZB || '0.00%',
        })) || [],

      // 地域分布
      regionData:
        data.jgfb?.map((item) => ({
          region: item.DS,
          code: item.JG || '',
          value: item.SL || 0,
          percentage: item.ZB || '0.00%',
        })) || [],

      // 原始数据
      raw: data,
    }
  } catch (error) {
    console.error('获取边境毒品成效地域数据失败:', error)
    return null
  }
}

export const getBorderAllData = async (dept = '45') => {
  try {
    const params = buildParams(dept)
    const data = await post('/api/djcxDashboard/fhgbjcxData', params)

    // 返回处理后的数据
    return {
      // 饼图数据 - 年龄和身份
      pieData: {
        organizer: {
          value: data.pie?.[0]?.ZZZ || 0,
          percentage: data.pie?.[0]?.ZZZZB || '0.00%',
        },
        transporter: {
          value: data.pie?.[0]?.YSZ || 0,
          percentage: data.pie?.[0]?.YSZZB || '0.00%',
        },
        crosser: {
          value: data.pie?.[0]?.TDZ || 0,
          percentage: data.pie?.[0]?.TDZZB || '0.00%',
        },
        ageGroups: {
          youth: {
            value: data.pie?.[0]?.ERALY || 0,
            percentage: data.pie?.[0]?.ERALYZB || '0.00%',
          },
          adult: {
            value: data.pie?.[0]?.ADULTHOOD || 0,
            percentage: data.pie?.[0]?.ADULTHOODZB || '0.00%',
          },
          middleAge: {
            value: data.pie?.[0]?.MIDDLE || 0,
            percentage: data.pie?.[0]?.MIDDLEZB || '0.00%',
          },
          elderly: {
            value: data.pie?.[0]?.OLD || 0,
            percentage: data.pie?.[0]?.OLDZB || '0.00%',
          },
        },
      },

      // 表格数据 - 人员情况
      tableData: {
        illegalExit: {
          value: data.table?.[0]?.FFCJ || 0,
          percentage: data.table?.[0]?.FFCJZB || '0.00%',
        },
        illegalEntry: {
          value: data.table?.[0]?.FFRJ || 0,
          percentage: data.table?.[0]?.FFRJZB || '0.00%',
        },
        smugglingExit: {
          value: data.table?.[0]?.ZSCJ || 0,
          percentage: data.table?.[0]?.ZSCJZB || '0.00%',
        },
        smugglingEntry: {
          value: data.table?.[0]?.ZSRJ || 0,
          percentage: data.table?.[0]?.ZSRJZB || '0.00%',
        },
        chinese: {
          value: data.table?.[0]?.ZGR || 0,
          percentage: data.table?.[0]?.ZGRZB || '0.00%',
          ffcjzgr: data.table?.[0]?.FFCJZGR || 0,
          ffcjzgrzb: data.table?.[0]?.FFCJZGRZB || 0,
          ffrjzgr: data.table?.[0]?.FFRJZGR || 0,
          ffrjzgrzb: data.table?.[0]?.FFRJZGRZB || 0,
          wbz: data.table?.[0]?.WBZZGR || 0,
          wbzzgrzb: data.table?.[0]?.WBZZGRZB || 0,
        },
        foreigners: {
          value: data.table?.[0]?.WGR || 0,
          percentage: data.table?.[0]?.WGRZB || '0.00%',
          ffcjwgr: data.table?.[0]?.FFCJWGR || 0,
          ffcjwgrzb: data.table?.[0]?.FFCJWGRZB || 0,
          ffrjwgr: data.table?.[0]?.FFRJWGR || 0,
          ffrjwgrzb: data.table?.[0]?.FFRJWGRZB || 0,
          wbz: data.table?.[0]?.WBZWGR || 0,
          wbzwgrzb: data.table?.[0]?.WBZWGRZB || 0,
        },

        criminalHistory: {
          value: data.table?.[0]?.BLQK || 0,
          percentage: data.table?.[0]?.BLQKZB || '0.00%',
          zs: data.table?.[0]?.QKRYZS || 0,
          zsPercentage: data.table?.[0]?.QKRYZB || '0.00%',
        },
        otherHistory: {
          value: data.table?.[0]?.QTQK || 0,
          percentage: data.table?.[0]?.QTQKZB || '0.00%',
        },
        gender: {
          male: {
            value: data.table?.[0]?.MAN || 0,
            percentage: data.table?.[0]?.MANZB || '0.00%',
          },
          female: {
            value: data.table?.[0]?.WOMEN || 0,
            percentage: data.table?.[0]?.WOMENZB || '0.00%',
          },
          total: {
            value: data.table?.[0]?.XBZS || 0,
            percentage: data.table?.[0]?.XBZSZB || '0.00%',
          },
        },
      },

      // 地域分布数据
      regionData:
        data.dyfb?.map((item) => ({
          region: item.DS,
          code: item.JG || '',
          value: item.SL || 0,
          percentage: item.ZB || '0.00%',
        })) || [],

      // 原始数据
      raw: data,
    }
  } catch (error) {
    console.error('获取妨害国边境成效-行政数据失败:', error)
    return null
  }
}

/**
 * 获取妨害国边境成效-行政数据
 * @param {String} dept 部门代码，默认为全省(45)
 * @returns {Promise<Object>} 处理后的数据
 */
export const getBorderViolationAdminData = async (dept = '45') => {
  try {
    const params = buildParams(dept)
    const data = await post('/api/djcxDashboard/fhgbjcxXzData', params)

    // 返回处理后的数据
    return {
      // 饼图数据 - 年龄和身份
      pieData: {
        organizer: {
          value: data.pie?.[0]?.ZZZ || 0,
          percentage: data.pie?.[0]?.ZZZZB || '0.00%',
        },
        transporter: {
          value: data.pie?.[0]?.YSZ || 0,
          percentage: data.pie?.[0]?.YSZZB || '0.00%',
        },
        crosser: {
          value: data.pie?.[0]?.TDZ || 0,
          percentage: data.pie?.[0]?.TDZZB || '0.00%',
        },
        ageGroups: {
          youth: {
            value: data.pie?.[0]?.ERALY || 0,
            percentage: data.pie?.[0]?.ERALYZB || '0.00%',
          },
          adult: {
            value: data.pie?.[0]?.ADULTHOOD || 0,
            percentage: data.pie?.[0]?.ADULTHOODZB || '0.00%',
          },
          middleAge: {
            value: data.pie?.[0]?.MIDDLE || 0,
            percentage: data.pie?.[0]?.MIDDLEZB || '0.00%',
          },
          elderly: {
            value: data.pie?.[0]?.OLD || 0,
            percentage: data.pie?.[0]?.OLDZB || '0.00%',
          },
        },
      },

      // 表格数据 - 人员情况
      tableData: {
        illegalExit: {
          value: data.table?.[0]?.FFCJ || 0,
          percentage: data.table?.[0]?.FFCJZB || '0.00%',
        },
        illegalEntry: {
          value: data.table?.[0]?.FFRJ || 0,
          percentage: data.table?.[0]?.FFRJZB || '0.00%',
        },
        smugglingExit: {
          value: data.table?.[0]?.ZSCJ || 0,
          percentage: data.table?.[0]?.ZSCJZB || '0.00%',
        },
        smugglingEntry: {
          value: data.table?.[0]?.ZSRJ || 0,
          percentage: data.table?.[0]?.ZSRJZB || '0.00%',
        },
        chinese: {
          value: data.table?.[0]?.ZGR || 0,
          percentage: data.table?.[0]?.ZGRZB || '0.00%',
          ffcjzgr: data.table?.[0]?.FFCJZGR || 0,
          ffcjzgrzb: data.table?.[0]?.FFCJZGRZB || 0,
          ffrjzgr: data.table?.[0]?.FFRJZGR || 0,
          ffrjzgrzb: data.table?.[0]?.FFRJZGRZB || 0,
          wbz: data.table?.[0]?.WBZZGR || 0,
          wbzzgrzb: data.table?.[0]?.WBZZGRZB || 0,
        },
        foreigners: {
          value: data.table?.[0]?.WGR || 0,
          percentage: data.table?.[0]?.WGRZB || '0.00%',
          ffcjwgr: data.table?.[0]?.FFCJWGR || 0,
          ffcjwgrzb: data.table?.[0]?.FFCJWGRZB || 0,
          ffrjwgr: data.table?.[0]?.FFRJWGR || 0,
          ffrjwgrzb: data.table?.[0]?.FFRJWGRZB || 0,
          wbz: data.table?.[0]?.WBZWGR || 0,
          wbzwgrzb: data.table?.[0]?.WBZWGRZB || 0,
        },

        criminalHistory: {
          value: data.table?.[0]?.BLQK || 0,
          percentage: data.table?.[0]?.BLQKZB || '0.00%',
          zs: data.table?.[0]?.QKRYZS || 0,
          zsPercentage: data.table?.[0]?.QKRYZB || '0.00%',
        },
        otherHistory: {
          value: data.table?.[0]?.QTQK || 0,
          percentage: data.table?.[0]?.QTQKZB || '0.00%',
        },
        gender: {
          total: {
            value: data.table?.[0]?.XBZS || 0,
            percentage: data.table?.[0]?.XBZSZB || '0.00%',
          },
          male: {
            value: data.table?.[0]?.MAN || 0,
            percentage: data.table?.[0]?.MANZB || '0.00%',
          },
          female: {
            value: data.table?.[0]?.WOMEN || 0,
            percentage: data.table?.[0]?.WOMENZB || '0.00%',
          },
        },
      },

      // 地域分布数据
      regionData:
        data.dyfb?.map((item) => ({
          region: item.DS,
          code: item.JG || '',
          value: item.SL || 0,
          percentage: item.ZB || '0.00%',
        })) || [],

      // 原始数据
      raw: data,
    }
  } catch (error) {
    console.error('获取妨害国边境成效-行政数据失败:', error)
    return null
  }
}

/**
 * 获取妨害国边境成效-刑事数据
 * @param {String} dept 部门代码，默认为全省(45)
 * @returns {Promise<Object>} 处理后的数据
 */
export const getBorderViolationCriminalData = async (dept = '45') => {
  try {
    // 注意：这个接口URL与行政数据相同，实际可能需要修改
    const params = buildParams(dept)
    const data = await post('/api/djcxDashboard/fhgbjcxXsData', params)

    // 返回处理后的数据结构与行政数据类似，但可能有细微差别
    return {
      // 饼图数据 - 年龄和身份
      pieData: {
        organizer: {
          value: data.pie?.[0]?.ZZZ || 0,
          percentage: data.pie?.[0]?.ZZZZB || '0.00%',
        },
        transporter: {
          value: data.pie?.[0]?.YSZ || 0,
          percentage: data.pie?.[0]?.YSZZB || '0.00%',
        },
        crosser: {
          value: data.pie?.[0]?.TDZ || 0,
          percentage: data.pie?.[0]?.TDZZB || '0.00%',
        },
        ageGroups: {
          youth: {
            value: data.pie?.[0]?.ERALY || 0,
            percentage: data.pie?.[0]?.ERALYZB || '0.00%',
          },
          adult: {
            value: data.pie?.[0]?.ADULTHOOD || 0,
            percentage: data.pie?.[0]?.ADULTHOODZB || '0.00%',
          },
          middleAge: {
            value: data.pie?.[0]?.MIDDLE || 0,
            percentage: data.pie?.[0]?.MIDDLEZB || '0.00%',
          },
          elderly: {
            value: data.pie?.[0]?.OLD || 0,
            percentage: data.pie?.[0]?.OLDZB || '0.00%',
          },
        },
      },

      // 表格数据 - 人员情况
      tableData: {
        illegalExit: {
          value: data.table?.[0]?.FFCJ || 0,
          percentage: data.table?.[0]?.FFCJZB || '0.00%',
        },
        illegalEntry: {
          value: data.table?.[0]?.FFRJ || 0,
          percentage: data.table?.[0]?.FFRJZB || '0.00%',
        },
        smugglingExit: {
          value: data.table?.[0]?.ZSCJ || 0,
          percentage: data.table?.[0]?.ZSCJZB || '0.00%',
        },
        smugglingEntry: {
          value: data.table?.[0]?.ZSRJ || 0,
          percentage: data.table?.[0]?.ZSRJZB || '0.00%',
        },
        chinese: {
          value: data.table?.[0]?.ZGR || 0,
          percentage: data.table?.[0]?.ZGRZB || '0.00%',
          ffcjzgr: data.table?.[0]?.FFCJZGR || 0,
          ffcjzgrzb: data.table?.[0]?.FFCJZGRZB || 0,
          ffrjzgr: data.table?.[0]?.FFRJZGR || 0,
          ffrjzgrzb: data.table?.[0]?.FFRJZGRZB || 0,
          wbz: data.table?.[0]?.WBZZGR || 0,
          wbzzgrzb: data.table?.[0]?.WBZZGRZB || 0,
        },
        foreigners: {
          value: data.table?.[0]?.WGR || 0,
          percentage: data.table?.[0]?.WGRZB || '0.00%',
          ffcjwgr: data.table?.[0]?.FFCJWGR || 0,
          ffcjwgrzb: data.table?.[0]?.FFCJWGRZB || 0,
          ffrjwgr: data.table?.[0]?.FFRJWGR || 0,
          ffrjwgrzb: data.table?.[0]?.FFRJWGRZB || 0,
          wbz: data.table?.[0]?.WBZWGR || 0,
          wbzwgrzb: data.table?.[0]?.WBZWGRZB || 0,
        },
        criminalHistory: {
          value: data.table?.[0]?.BLQK || 0,
          percentage: data.table?.[0]?.BLQKZB || '0.00%',
          zs: data.table?.[0]?.QKRYZS || 0,
          zsPercentage: data.table?.[0]?.QKRYZB || '0.00%',
        },
        otherHistory: {
          value: data.table?.[0]?.QTQK || 0,
          percentage: data.table?.[0]?.QTQKZB || '0.00%',
        },
        gender: {
          total: {
            value: data.table?.[0]?.XBZS || 0,
            percentage: data.table?.[0]?.XBZSZB || '0.00%',
          },
          male: {
            value: data.table?.[0]?.MAN || 0,
            percentage: data.table?.[0]?.MANZB || '0.00%',
          },
          female: {
            value: data.table?.[0]?.WOMEN || 0,
            percentage: data.table?.[0]?.WOMENZB || '0.00%',
          },
        },
      },

      // 地域分布数据
      regionData:
        data.dyfb?.map((item) => ({
          region: item.DS,
          code: item.JG || '',
          value: item.SL || 0,
          percentage: item.ZB || '0.00%',
        })) || [],

      // 原始数据
      raw: data,
    }
  } catch (error) {
    console.error('获取妨害国边境成效-刑事数据失败:', error)
    return null
  }
}

/**
 * 获取涉枪案件打击成效数据
 * @param {String} dept 部门代码，默认为全省(45)
 * @returns {Promise<Object>} 处理后的数据
 */
export const getGunCrimeEffectData = async (dept = '45') => {
  try {
    const params = buildParams(dept)
    const data = await post('/api/djcxDashboard/sqajdjcxData', params)

    // 返回处理后的数据
    return {
      // 表格数据
      tableData: {
        gunCrime: {
          zzqz: data.table?.[0]?.ZZQZ || 0,
          zsqz: data.table?.[0]?.ZSQZ || 0,
          sazs: data.table?.[0]?.SAZS || 0,
        },
        gender: {
          male: {
            value: data.table?.[0]?.MAN || 0,
            percentage: data.table?.[0]?.MANZB || '0.00%',
          },
          female: {
            value: data.table?.[0]?.WOMEN || 0,
            percentage: data.table?.[0]?.WOMENZB || '0.00%',
          },
          zs: {
            value: data.table?.[0]?.QKRYZS || 0,
            percentage: data.table?.[0]?.QKRYZB || '0.00%',
          },
        },
        criminalHistory: {
          value: data.table?.[0]?.BLQK || 0,
          percentage: data.table?.[0]?.BLQKZB || '0.00%',
          zs: data.table?.[0]?.QKRYZS || 0,
          zsPercentage: data.table?.[0]?.QKRYZB || '0.00%',
        },
        otherHistory: {
          value: data.table?.[0]?.QTQK || 0,
          percentage: data.table?.[0]?.QTQKZB || '0.00%',
        },
        ageGroups: {
          youth: {
            value: data.table?.[0]?.ERALY || 0,
            percentage: data.table?.[0]?.ERALYZB || '0.00%',
          },
          adult: {
            value: data.table?.[0]?.ADULTHOOD || 0,
            percentage: data.table?.[0]?.ADULTHOODZB || '0.00%',
          },
          middleAge: {
            value: data.table?.[0]?.MIDDLE || 0,
            percentage: data.table?.[0]?.MIDDLEZB || '0.00%',
          },
          elderly: {
            value: data.table?.[0]?.OLD || 0,
            percentage: data.table?.[0]?.OLDZB || '0.00%',
          },
        },
      },

      // 地域分布数据
      regionData:
        data.dyfb?.map((item) => ({
          region: item.DS,
          code: item.JG || '',
          value: item.SL || 0,
          percentage: item.ZB || '0.00%',
        })) || [],

      // 原始数据
      raw: data,
    }
  } catch (error) {
    console.error('获取涉枪案件打击成效数据失败:', error)
    return null
  }
}

/**
 * 获取出入境打击成效数据
 * @param {String} dept 部门代码，默认为全省(45)
 * @returns {Promise<Object>} 处理后的数据
 */
export const getImmigrationEffectData = async (dept = '45') => {
  try {
    const params = buildParams(dept)
    const data = await post('/api/djcxDashboard/crjdjcxData', params)

    // 返回处理后的数据
    return {
      // 立案数
      caseCount: {
        value: data.LAS || 0,
        sameRatio: data.LASTB || '0.00%',
        sameRatioStatus: data.LASTBZT === 1, // 1表示上升，0表示下降
        periodRatio: data.LASYB || '0.00%',
        periodRatioStatus: data.LASYBZT === 1,
      },
      // 抓获总数
      captureCount: {
        value: data.ZHZS || 0,
        sameRatio: data.ZHZSTB || '0.00%',
        sameRatioStatus: data.ZHZSTBZT === 1,
        periodRatio: data.ZHZSYB || '0.00%',
        periodRatioStatus: data.ZHZSYBZT === 1,
      },
      // 刑拘总数
      detentionCount: {
        value: data.XJZS || 0,
        sameRatio: data.XJZSTB || '0.00%',
        sameRatioStatus: data.XJZSTBZT === 1,
        periodRatio: data.XJZSYB || '0.00%',
        periodRatioStatus: data.XJZSYBZT === 1,
      },
      // 原始数据
      raw: data,
    }
  } catch (error) {
    console.error('获取出入境打击成效数据失败:', error)
    return null
  }
}

/**
 * 获取外国人申请单位审批统计数据
 * @param {String} dept 部门代码，默认为全省(45)
 * @returns {Promise<Object>} 处理后的数据
 */
export const getForeignerApprovalData = async (dept = '45') => {
  try {
    const params = buildParams(dept)
    const data = await post('/api/dashboard/wgrsqSpdwTj', params)

    // 返回处理后的数据
    return {
      // 各签证处数据
      visaStations: {
        nanning: data['459014060000'] || 0, // 南宁机场口岸签证处
        guilin: data['459015060000'] || 0, // 桂林机场口岸签证处
        youyi: data['459418060000'] || 0, // 友谊关口岸签证处
        dongxing: data['459443060000'] || 0, // 东兴口岸签证处
      },
      // 原始数据
      raw: data,
    }
  } catch (error) {
    console.error('获取外国人申请单位审批统计数据失败:', error)
    return null
  }
}

/**
 * 获取边境毒品成效趋势数据
 * @returns {Promise<Object>} 处理后的数据
 */
export const getBorderDrugTrendData = async () => {
  try {
    const res = await post('/api/djcxDashboard/bjdpcxData', {
      startTime: dayjs().subtract(30, 'day').format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      dept: '45',
    })
    // 假设返回数据结构为 { dataDpjgLess: [...], dataDpjgMore: [...] }
    const less = res.dataDpjgLess || []
    const more = res.dataDpjgMore || []
    console.log('less', less)
    console.log('more', more)

    // 横坐标（季度）
    const xData = less.map((item) => item.JD)
    // 纵坐标
    const lessData = less.map((item) => item.SL)
    const moreData = more.map((item) => item.SL)
    console.log('api lessData', lessData)
    console.log('api moreData', moreData)
    console.log('api xData', xData)

    return {
      xData,
      lessData,
      moreData,
    }
  } catch (e) {
    console.error('趋势图数据获取失败', e)
    return null
  }
}

/**
 * 获取线索来源数据
 * @returns {Promise<Object>} 处理后的数据
 */
export const getClueSourceData = async () => {
  try {
    const params = buildParams()
    const data = await post('/api/djcxDashboard/xslyData', params)

    return {
      total: data.zs || 0, // 总数
      modelDiscovery: data.jmfx || 0, // 建模发现
      humanIntelligence: data.rlqb || 0, // 人力情报
      patrolCheck: data.xlcc || 0, // 巡逻查处
      voluntarySurrender: data.zdta || 0, // 主动投案
      caseDiscovery: data.azafx || 0, // 案中案发现
      publicReport: data.qzjb || 0, // 群众举报
      other: data.qt || 0, // 其他
      overseasReview: data.jwscqf || 0, // 境外审查遣返
    }
  } catch (error) {
    console.error('获取线索来源数据失败:', error)
    return null
  }
}

export default {
  getBorderDrugEffectData,
  getBorderViolationAdminData,
  getBorderViolationCriminalData,
  getGunCrimeEffectData,
  getImmigrationEffectData,
  getBorderDrugRegionData,
  getForeignerApprovalData,
  getBorderDrugTrendData,
  getClueSourceData,
  getBorderAllData,
}
