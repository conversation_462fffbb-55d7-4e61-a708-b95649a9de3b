import { get, post, put, del } from '@/utils/http'

/**
 * 通用接口管理
 */

// 登录认证相关接口
export const auth = {
  // 登录
  login: (data) => post('/api/logintoken', data),
  // 登出
  logout: () => post('/api/logout'),
  // 获取用户信息
  getUserInfo: () => get('/api/userInfo'),
  // 根据token获取用户信息
  userInfoByToken: (token) => get('/api/loginInfo', {}, { headers: { Authorization: token } }),
  // 刷新token
  refreshToken: () => post('/api/refreshToken'),
}

// 用户管理相关接口
export const user = {
  // 获取用户列表
  getList: (params) => get('/user/list', params),
  // 获取用户详情
  getDetail: (id) => get(`/user/${id}`),
  // 创建用户
  create: (data) => post('/user', data),
  // 更新用户
  update: (id, data) => put(`/user/${id}`, data),
  // 删除用户
  delete: (id) => del(`/user/${id}`),
  // 更新用户状态
  updateStatus: (id, status) => put(`/user/${id}/status`, { status }),
  // 重置密码
  resetPassword: (id) => put(`/user/${id}/password/reset`),
}

// 通行记录相关接口
export const passage = {
  // 获取通行记录列表
  getList: (params) => get('/passage/records', params),
  // 获取通行人员户籍地统计
  getPersonResidenceStats: (params) => get('/passage/personResidenceStats', params),
  // 获取通行车辆归属地统计
  getVehicleOriginStats: (params) => get('/passage/vehicleOriginStats', params),
  // 获取通行数据趋势
  getTrendData: (params) => get('/passage/trendData', params),
  // 获取异常通行记录
  getAbnormalRecords: (params) => get('/passage/abnormalRecords', params),
}

// 预警信息相关接口
export const warning = {
  // 获取预警信息统计
  getStats: (params) => get('/warning/stats', params),
  // 获取预警信息列表
  getList: (params) => get('/warning/list', params),
  // 处理预警信息
  process: (id, data) => put(`/warning/${id}/process`, data),
  // 忽略预警信息
  ignore: (id) => put(`/warning/${id}/ignore`),
  // 批量处理预警信息
  batchProcess: (ids, data) => put('/warning/batchProcess', { ids, ...data }),
  // 获取预警分类统计
  getTypeStats: (params) => get('/warning/typeStats', params),
}

// 系统管理相关接口
export const system = {
  // 获取系统参数
  getConfig: () => get('/system/config'),
  // 更新系统参数
  updateConfig: (data) => put('/system/config', data),
  // 获取日志列表
  getLogs: (params) => get('/system/logs', params),
  // 获取操作日志
  getOperationLogs: (params) => get('/system/operationLogs', params),
  // 清理日志
  clearLogs: (type) => del('/system/logs', { type }),
}

// 仪表盘数据接口
export const dashboard = {
  // 妨害国边境违法犯罪
  getFhgbjData: (data) => post('/dashboard/fhgbjData', data),

  // 走私案件情况
  getZsajqkData: (data) => post('/dashboard/zsajqkData', data),

  // 边境涉毒涉枪犯罪
  getBjsdsqfzData: (data) => post('/dashboard/bjsdsqfzData', data),

  // 各地妨害国边境各市情况
  getGdfhgbjData: (data) => post('/dashboard/gdfhgbjData', data),

  // 走私类各市情况
  getGdzslData: (data) => post('/dashboard/gdzslData', data),

  // 毒品类各市情况 (与getGdfhgbjData使用相同的接口地址，但参数不同)
  getDplgsqkData: (data) => post('/dashboard/gdfhgbjData', data),
}

// 导出所有API接口
export default {
  auth,
  user,
  passage,
  warning,
  system,
  dashboard,
}
