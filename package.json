{"name": "border-control-frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "download-fonts": "node download-fonts.js", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.8.4", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.9.8", "lodash-es": "^4.17.21", "pinia": "^3.0.1", "v-scale-screen": "^2.3.0", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@vitejs/plugin-legacy": "^6.0.2", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "globals": "^16.0.0", "prettier": "3.5.3", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.5.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}