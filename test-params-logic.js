// 测试参数逻辑
function testParamsLogic() {
  console.log('=== 测试接口参数逻辑 ===')
  
  // 模拟 buildParams 函数
  function buildParams(dateRange, code) {
    return {
      startDate: dateRange[0],
      endDate: dateRange[1],
      code: code
    }
  }
  
  // 测试场景1：选择普通城市选项
  console.log('\n场景1：选择普通城市选项（如南宁市）')
  let selecteDept = '450100000000'
  let params = buildParams(['2024-01-01', '2024-12-31'], '45')
  
  if (selecteDept) {
    params.dept = selecteDept
  }
  
  if (selecteDept && selecteDept.endsWith('_GA')) {
    params.type = 'dfga'
  }
  
  console.log('请求参数:', params)
  console.log('是否包含 type: "dfga":', params.type === 'dfga')
  
  // 测试场景2：选择公安选项
  console.log('\n场景2：选择公安选项（如百色公安）')
  selecteDept = '451000000000_GA'
  params = buildParams(['2024-01-01', '2024-12-31'], '45')
  
  if (selecteDept) {
    params.dept = selecteDept
  }
  
  if (selecteDept && selecteDept.endsWith('_GA')) {
    params.type = 'dfga'
  }
  
  console.log('请求参数:', params)
  console.log('是否包含 type: "dfga":', params.type === 'dfga')
  
  // 测试场景3：选择崇左公安
  console.log('\n场景3：选择崇左公安')
  selecteDept = '451400000000_GA'
  params = buildParams(['2024-01-01', '2024-12-31'], '45')
  
  if (selecteDept) {
    params.dept = selecteDept
  }
  
  if (selecteDept && selecteDept.endsWith('_GA')) {
    params.type = 'dfga'
  }
  
  console.log('请求参数:', params)
  console.log('是否包含 type: "dfga":', params.type === 'dfga')
  
  // 测试场景4：选择防城港公安
  console.log('\n场景4：选择防城港公安')
  selecteDept = '450600000000_GA'
  params = buildParams(['2024-01-01', '2024-12-31'], '45')
  
  if (selecteDept) {
    params.dept = selecteDept
  }
  
  if (selecteDept && selecteDept.endsWith('_GA')) {
    params.type = 'dfga'
  }
  
  console.log('请求参数:', params)
  console.log('是否包含 type: "dfga":', params.type === 'dfga')
  
  // 测试场景5：未选择任何单位
  console.log('\n场景5：未选择任何单位')
  selecteDept = ''
  params = buildParams(['2024-01-01', '2024-12-31'], '45')
  
  if (selecteDept) {
    params.dept = selecteDept
  }
  
  if (selecteDept && selecteDept.endsWith('_GA')) {
    params.type = 'dfga'
  }
  
  console.log('请求参数:', params)
  console.log('是否包含 type: "dfga":', params.type === 'dfga')
}

// 运行测试
testParamsLogic()
