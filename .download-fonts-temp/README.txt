字体下载说明
==========

请按照以下步骤操作，以确保项目在内网环境中能正确显示字体：

1. 运行以下命令安装所需的字体：

```bash
npm run download-fonts
```

2. 确认字体文件已下载到 src/assets/fonts 目录中：
   - MicrosoftYaHei.ttf
   - MicrosoftYaHei-Bold.ttf
   - PingFangSC-Regular.ttf
   - DINAlternate-Bold.ttf
   - BebasNeue-Regular.ttf

3. 如果自动下载失败，请手动从以下链接下载字体文件，并放入 src/assets/fonts 目录：

   - 微软雅黑常规体 (MicrosoftYaHei.ttf):
     https://github.com/Richasy/Font-Downloader/raw/master/fonts/MSYaHei.ttf

   - 微软雅黑粗体 (MicrosoftYaHei-Bold.ttf):
     https://github.com/Richasy/Font-Downloader/raw/master/fonts/MSYaHei-Bold.ttf

   - 苹方常规体 (PingFangSC-Regular.ttf):
     https://github.com/MeowOhMeow/Hackintosh-MSI-GE62VR/raw/master/Fonts/PingFang%20SC/PingFangSC-Regular.ttf

   - DIN Alternate粗体 (DINAlternate-Bold.ttf):
     https://github.com/shinnytech/shinny-futures-web/raw/master/static/DINAlternate-Bold.ttf

   - Bebas Neue常规体 (BebasNeue-Regular.ttf):
     https://github.com/dharmatype/Bebas-Neue/raw/master/fonts/webfonts/woff/BebasNeue-Regular.woff

4. 详细的字体使用说明，请参阅项目根目录下的 README-fonts.md 文件。

注意：
项目中已有庞门正道标题体（PangMenZhengDaoBiaoTiTi-1.ttf），不需要额外下载。 