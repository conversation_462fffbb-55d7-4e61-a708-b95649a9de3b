---
description: 
globs: 
alwaysApply: false
---
# 项目结构概述

这是一个基于Vue 3和Vite的前端项目，使用了Vue Router进行路由管理和Pinia进行状态管理。

## 主要文件和目录

- [src/main.js](mdc:src/main.js) - 项目入口文件，负责创建Vue应用实例并挂载
- [src/App.vue](mdc:src/App.vue) - 根组件
- [src/router/index.js](mdc:src/router/index.js) - 路由配置
- [src/stores/](mdc:src/stores) - Pinia状态管理
- [src/views/](mdc:src/views) - 页面级组件
- [src/components/](mdc:src/components) - 可复用组件
- [src/assets/](mdc:src/assets) - 静态资源
- [public/](mdc:public) - 公共资源目录
