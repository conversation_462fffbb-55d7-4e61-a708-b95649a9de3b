---
description: 
globs: 
alwaysApply: false
---
# 大屏布局与交互

## 布局概览

边海管控驾驶舱大屏分为9个主要区域，布局如下：

```
+----------------+-------------+----------------+
|                |             |                |
|   LeftTop      |   Center    |   RightTop    |
|   420x308      |   Route     |   420x308     |
|                |   Area      |                |
+----------------+             +----------------+
|                |             |                |
|   LeftBottom   |             |   RightBottom |
|   420x308      |             |   420x308     |
|                |             |                |
+----------------+-------------+----------------+
+--------+--------+----------+--------+
| Bottom1| Bottom2| Bottom3  |Bottom4 |
| 420x308| 420x308| 540x308  |420x308|
+--------+--------+----------+--------+
```

## 区域描述

1. **title** - 顶部标题区域
   - 显示"边海管控驾驶舱"主标题
   - 包含系统时间、日期和登录信息

2. **lefttop** - 左上区域
   - 显示"防范国边境违法犯罪"相关统计数据
   - 包含"刑事立案"和"行政立案"两块子面板
   - 显示同比增长率和数值

3. **leftbottom** - 左下区域
   - 显示"走私案件情况"统计数据
   - 包含立案数、破案数、刑拘人数等指标
   - 采用六边形卡片样式展示

4. **righttop** - 右上区域
   - 显示"口岸边防检查情况"
   - 包含入境人数、出境人数等指标
   - 展示各类车辆、飞机、船舶、火车数据及百分比

5. **rightbottom** - 右下区域
   - 显示"边境检查站过往情况"
   - 包含车辆通行和人员通行数据
   - 区分进入边境和离开边境两种情况

6. **center** - 中心区域（路由可切换）
   - 默认显示广西地图，带有城市名称和边界
   - 地图上有数据点和发光效果
   - 可通过点击其他区域的特定按钮切换显示不同内容
   - 集成了基础信息、预警信息、打击成效、督导执行等模块

7. **bottom1** - 底部区域1
   - 显示"边境毒品犯罪"相关统计
   - 包含立案数、破案数等指标

8. **bottom2** - 底部区域2
   - 显示"边境走私犯罪"相关统计
   - 包含立案数、缴获数等指标

9. **bottom3** - 底部区域3
   - 显示各地区案件统计图表
   - 以柱状图形式展示各地区数据

10. **bottom4** - 底部区域4
    - 显示"出入境证件办理情况"
    - 包含中国公民和外国人证件办理统计

## 交互规则

1. **中心区域切换**
   - 点击顶部导航栏中的"基础信息"、"预警信息"、"打击成效"、"督导执行"按钮，可切换中心区域内容
   - 点击某些区域内的详情按钮，会跳转到相应的详情页面，替换中心区域内容
   - 全屏按钮可将地图放大到全屏显示

2. **数据联动**
   - 某些区域的数据可能与中心区域的内容联动，如地图上选择某个地区，周边统计数据会相应更新
   - 时间选择器可影响全局数据的时间范围

## 组件开发指南

1. **布局实现**
   - 使用CSS Grid或Flex布局实现整体结构
   - 所有区域应按照1920x1080的设计稿实现，然后通过VScaleScreen进行适配

2. **组件拆分**
   - 创建[src/components/dashboard/](mdc:src/components/dashboard)目录存放所有大屏组件
   - 每个区域应拆分为独立组件，如：
     - DashboardTitle.vue - 顶部标题组件
     - LeftTopPanel.vue - 左上面板组件
     - CenterMap.vue - 中心地图组件
     - 等等

3. **路由配置**
   - 中心区域使用嵌套路由实现内容切换
   - 配置示例：
   ```js
   const routes = [
     {
       path: '/dashboard',
       component: DashboardView,
       children: [
         { 
           path: 'basic',
           component: () => import('@/views/dashboard/BasicInfoView.vue')
         },
         {
           path: 'warning',
           component: () => import('@/views/dashboard/WarningInfoView.vue')
         },
         // 其他子路由...
       ]
     }
   ]
   ```

4. **数据可视化**
   - 推荐使用ECharts实现各类图表
   - 地图可使用ECharts的地图组件或百度/高德地图API

## 设计风格

- 整体采用深蓝色科技风背景
- 数据卡片使用半透明效果
- 重要数据使用明亮的蓝色、绿色等高对比度颜色
- 边框和分割线采用科技感线条和光效
- 字体建议使用等宽字体增强科技感

## 资源文件组织

项目静态资源已经按照以下结构组织在 [src/assets/dashboard/](mdc:src/assets/dashboard) 目录下：

```
src/assets/dashboard/
├── backgrounds/  - 背景图片
│   ├── main-bg.png              - 大屏背景
│   ├── trend-bg.png             - 中间趋势背景
│   ├── drug-crime-bg.png        - 边境毒品犯罪背景
│   ├── module-bg-default.png    - 模块背景(默认状态)
│   ├── module-bg-active.png     - 模块背景(选中状态)
│   ├── small-module-bg.png      - 小模块内容背景
│   ├── criminal-detain-bg.png   - 刑拘人数背景
│   ├── criminal-case-bg.png     - 刑事立案数据背景
│   └── admin-case-bg.png        - 行政立案数据背景
│
├── icons/  - 图标
│   ├── time.png                 - 时间图标
│   ├── date.png                 - 日期图标
│   ├── user.png                 - 用户图标
│   ├── home.png                 - 首页图标
│   ├── fullscreen.png           - 全屏图标
│   ├── case-filing.png          - 立案图标
│   ├── case-solved.png          - 破案数图标
│   ├── criminal-detention.png   - 刑拘人数图标
│   ├── border-entry.png         - 出入境图标
│   ├── vehicle-passage.svg      - 车辆通行图标
│   ├── person-passage.png       - 人员通行图标
│   └── ...                      - 其他图标
│
├── buttons/  - 按钮
│   ├── home-btn.png             - 首页按钮
│   ├── fullscreen-btn.png       - 全屏按钮
│   ├── basic-info-default.png   - 基础信息按钮(默认)
│   ├── warning-info-default.png - 预警信息按钮(默认)
│   ├── effect-default.png       - 打击成效按钮(默认)
│   └── supervision-default.png  - 督导盯办按钮
│
├── charts/  - 图表相关
│   ├── case-filing.png          - 立案数图表
│   ├── case-solved.png          - 破案数图表
│   ├── gun-case-filing.png      - 涉枪立案数
│   └── gun-case-solved.png      - 涉枪破案数
│
└── decorations/  - 装饰元素
    ├── title-large.png          - 大标题
    ├── title-small.png          - 小标题
    ├── title-small-long.png     - 小标题(长)
    ├── decoration1.png          - 装饰1
    ├── decoration2.png          - 装饰2
    └── pie-chart-decoration.png - 饼图中间装饰
```

### 资源使用示例

在Vue组件中引用这些资源的示例：

```vue
<template>
  <div class="dashboard-container">
    <img :src="mainBg" class="main-background">
    <div class="title-area">
      <img :src="titleLarge" alt="边海管控驾驶舱">
      <div class="time-info">
        <img :src="timeIcon" alt="时间">
        <span>{{ currentTime }}</span>
      </div>
    </div>
    <!-- 其他内容 -->
  </div>
</template>

<script setup>
// 导入资源
import mainBg from '@/assets/dashboard/backgrounds/main-bg.png'
import titleLarge from '@/assets/dashboard/decorations/title-large.png'
import timeIcon from '@/assets/dashboard/icons/time.png'
import { ref } from 'vue'

// 时间处理
const currentTime = ref(new Date().toLocaleTimeString())
setInterval(() => {
  currentTime.value = new Date().toLocaleTimeString()
}, 1000)
</script>

<style scoped>
.dashboard-container {
  position: relative;
  width: 1920px;
  height: 1080px;
}

.main-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.title-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
}

.time-info {
  display: flex;
  align-items: center;
  color: #30a8ff;
}

.time-info img {
  margin-right: 8px;
}
</style>