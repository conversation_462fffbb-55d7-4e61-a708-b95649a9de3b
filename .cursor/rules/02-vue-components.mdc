---
description: 
globs: 
alwaysApply: false
---
# Vue组件开发规范

## 组件结构

项目使用Vue 3的组合式API (Composition API)开发组件。组件通常遵循以下结构：

```vue
<script setup>
// 导入
import { ref, computed, onMounted } from 'vue'
import { useStore } from '../stores/counter'

// 状态定义
const count = ref(0)

// 计算属性
const doubleCount = computed(() => count.value * 2)

// 方法
function increment() {
  count.value++
}

// 生命周期钩子
onMounted(() => {
  console.log('组件已挂载')
})
</script>

<template>
  <div>
    <!-- 组件模板 -->
  </div>
</template>

<style scoped>
/* 组件样式 */
</style>
```

## 组件目录

- [src/components/](mdc:src/components) - 可复用的通用组件
- [src/views/](mdc:src/views) - 页面级组件，通常对应路由

## 示例组件

- [src/components/HelloWorld.vue](mdc:src/components/HelloWorld.vue) - 基础组件示例
- [src/views/HomeView.vue](mdc:src/views/HomeView.vue) - 首页视图组件
