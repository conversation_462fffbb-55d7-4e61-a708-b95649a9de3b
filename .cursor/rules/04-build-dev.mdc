---
description: 
globs: 
alwaysApply: false
---
# 项目构建和开发

## 开发环境

项目使用Vite作为构建工具和开发服务器，配置文件是[vite.config.js](mdc:vite.config.js)。

主要命令：
- `pnpm run dev` - 启动开发服务器
- `pnpm run build` - 构建生产版本
- `pnpm run preview` - 本地预览生产构建
- `pnpm run lint` - 代码检查
- `pnpm run format` - 代码格式化

## 代码规范

项目使用ESLint和Prettier进行代码规范检查和格式化：
- [eslint.config.js](mdc:eslint.config.js) - ESLint配置
- [.prettierrc.json](mdc:.prettierrc.json) - Prettier配置

## 项目依赖

主要依赖项见[package.json](mdc:package.json)：
- Vue 3 - 前端框架
- Vue Router - 路由管理
- Pinia - 状态管理
- Vite - 构建工具
- ESLint/Prettier - 代码规范
