---
description: 
globs: 
alwaysApply: false
---
# 路由和状态管理

## 路由配置

项目使用Vue Router进行路由管理，主要配置文件是[src/router/index.js](mdc:src/router/index.js)。

路由定义示例：
```js
const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/about',
      name: 'about',
      // 路由懒加载
      component: () => import('../views/AboutView.vue'),
    },
  ],
})
```

## 状态管理

项目使用Pinia进行状态管理，store定义在[src/stores/](mdc:src/stores)目录。

示例store：[src/stores/counter.js](mdc:src/stores/counter.js)

使用方式：
```js
// 在组件中导入和使用store
import { useCounterStore } from '@/stores/counter'

const counterStore = useCounterStore()
console.log(counterStore.count)
counterStore.increment()
```
