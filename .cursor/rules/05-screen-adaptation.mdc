---
description: 
globs: 
alwaysApply: false
---
# 大屏适配方案

## 适配原理

项目使用VScaleScreen组件实现大屏自适应。该方案会根据设计稿尺寸（1920x1080）与实际屏幕尺寸的比例关系，对内容进行等比例缩放，确保在不同分辨率的屏幕上都能完整显示设计内容。

## 配置文件

主要配置在以下文件：

- [src/App.vue](mdc:src/App.vue) - 使用VScaleScreen组件包裹根内容
- [index.html](mdc:index.html) - HTML元数据和基础样式设置

## 使用方法

1. 在App.vue中导入VScaleScreen组件：
```js
import VScaleScreen from 'v-scale-screen'
```

2. 在App.vue中使用VScaleScreen组件包裹内容：
```html
<VScaleScreen :width="1920" :height="1080">
  <RouterView />
</VScaleScreen>
```

3. 组件设计时按照1920x1080尺寸设计，如[src/views/HomeView.vue](mdc:src/views/HomeView.vue)中的示例。

## 配置选项

VScaleScreen组件支持以下属性：

- width: 设计稿宽度（默认1920px）
- height: 设计稿高度（默认1080px）
- fullScreen: 是否全屏显示
- autoScale: 是否自动缩放
- ignoreResize: 是否忽略窗口大小变化
- delay: 窗口变化后的延迟响应时间（毫秒）
