---
description: 
globs: 
alwaysApply: false
---
# 边海管控项目前端开发规范

本规则总结了边海管控项目前端开发的设计规范和技术约定。

## 1. 面板布局规范
- 所有面板组件应继承自 [BasePanel.vue](mdc:src/components/dashboard/BasePanel.vue) 组件
- 面板标题使用金色调(#FEDE4B)，字体使用粗体
- 面板子标题位置居中对齐
- 背景图片使用绝对定位，z-index设为-1

## 2. 数据展示面板
### [BottomPanel1.vue](mdc:src/components/dashboard/BottomPanel1.vue) 实现
- 使用网格布局，分为若干统计卡片
- 每个卡片包含图标、数值、标题和同环比数据
- 同环比数据使用上下箭头图标（up.png/down.png）
- 同比上升使用红色(#FF695A)，下降使用青色(#8DFFD5)
- 图标尺寸为72x72像素

### [BottomPanel2.vue](mdc:src/components/dashboard/BottomPanel2.vue) 实现
- 垂直布局设计，每行包含一个统计项
- 每行包含图标、标题、数值和同环比数据
- 左侧图标使用76x76像素的统一尺寸
- 标题文字使用粗体，数值使用特殊字体

## 3. 图表视图规范
### [ChartView.vue](mdc:src/views/ChartView.vue) 实现
- 使用vue-echarts组件实现
- 按需引入echarts核心组件降低体积
- 图表布局：2x2网格布局
- 图表颜色：
  - 近12个月数据：橙色(#FF8E3C)
  - 前12个月数据：蓝色(#00C6FF)
- 折线图特性：
  - 添加线性渐变阴影效果
  - 标记线位于5月处
  - 每条线包含一个圆形标记点
- 图表标题使用金黄色(#FEDE4B)，居中显示，距顶部10px

## 4. 技术约定
- 使用vue-echarts代替原生echarts接口
- 组件使用SFC模式编写
- 使用reactive和ref管理状态
- 图表使用自动调整大小功能(autoresize)

## 5. 样式规范
- 主要文本颜色：#E5FAFF
- 数据上升/下降颜色对应：
  - 上升：红色(#FF695A)
  - 下降：青色(#8DFFD5)
- 图标引用路径：@/assets/dashboard/icons/
- 背景图片引用路径：@/assets/dashboard/backgrounds/
- 标题字体：MicrosoftYaHei-Bold
- 数值字体：PangMenZhengDao
